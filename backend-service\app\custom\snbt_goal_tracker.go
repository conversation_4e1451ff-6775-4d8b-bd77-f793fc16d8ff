package custom

import (
	"context"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/microcosm-cc/bluemonday"
)

// SnbtGoalTrackerInput represents the input data for the SNBT goal tracker form
type SnbtGoalTrackerInput struct {
	Name                     string `json:"name" binding:"required"`
	Email                    string `json:"email" binding:"required,email"`
	PenalaranUmum            int    `json:"penalaran_umum" binding:"required,min=0"`
	PengetahuanKuantitatif   int    `json:"pengetahuan_kuantitatif" binding:"required,min=0"`
	PengetahuanPemahamanUmum int    `json:"pengetahuan_pemahaman_umum" binding:"required,min=0"`
	PemahamanBacaanMenulis   int    `json:"pemahaman_bacaan_menulis" binding:"required,min=0"`
	LiterasiBahasaIndonesia  int    `json:"literasi_bahasa_indonesia" binding:"required,min=0"`
	LiterasiBahasaInggris    int    `json:"literasi_bahasa_inggris" binding:"required,min=0"`
	PenalaranMatematika      int    `json:"penalaran_matematika" binding:"required,min=0"`
	PassedSnbt               bool   `json:"passed_snbt"`
	FeltHelped               bool   `json:"felt_helped"`
	HelpfulnessRating        int    `json:"helpfulness_rating" binding:"required,min=1,max=10"`
	MostHelpfulAspect        string `json:"most_helpful_aspect"`
	ImprovementSuggestions   string `json:"improvement_suggestions"`
	ContactConsent           bool   `json:"contact_consent"`
	PhoneNumber              string `json:"phone_number"`
}

// SnbtGoalTrackerResult represents the result of a SNBT goal tracker submission
type SnbtGoalTrackerResult struct {
	ID        int       `json:"id" db:"id"`
	Email     string    `json:"email" db:"email"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
}

// RegisterSnbtGoalTrackerRoutes registers the routes for the SNBT goal tracker
func RegisterSnbtGoalTrackerRoutes(r *gin.Engine, dbx *sqlx.DB) {
	v0 := r.Group("/v0")
	{
		v0.POST("/snbt-goal-tracker", submitSnbtGoalTracker(dbx))
		v0.GET("/snbt-goal-tracker/check-submission", checkSnbtGoalTrackerSubmission(dbx))
	}
}

// submitSnbtGoalTracker handles the submission of the SNBT goal tracker form
func submitSnbtGoalTracker(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Create a context with timeout for all database operations
		ctx, cancel := context.WithTimeout(c.Request.Context(), 5*time.Second)
		defer cancel()

		var input SnbtGoalTrackerInput

		if err := c.ShouldBindJSON(&input); err != nil {
			log.Printf("[SNBT Goal Tracker] Invalid form data: %v", err)
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "Invalid form data: " + err.Error(),
			})
			return
		}

		log.Printf("[SNBT Goal Tracker] Processing form submission for user: %s (%s)", input.Name, input.Email)

		// Start transaction
		tx, err := dbx.BeginTxx(ctx, nil)
		if err != nil {
			log.Printf("[SNBT Goal Tracker] Error starting transaction: %v", err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Failed to start database transaction",
			})
			return
		}

		// Ensure transaction is rolled back if not committed
		defer func() {
			if tx != nil {
				tx.Rollback()
			}
		}()

		// Check if user has already submitted the form using the optimized query
		var submissionResult struct {
			HasSubmitted    bool `db:"has_submitted"`
			SubmissionCount int  `db:"submission_count"`
		}

		err = tx.GetContext(ctx, &submissionResult, `
			SELECT
				EXISTS(SELECT 1 FROM snbt_goal_tracker WHERE email = $1 LIMIT 1) as has_submitted,
				(SELECT COUNT(*) FROM snbt_goal_tracker WHERE email = $1) as submission_count
		`, input.Email)

		if err != nil {
			log.Printf("[SNBT Goal Tracker] Error checking existing submission for %s: %v", input.Email, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Failed to check existing submission",
			})
			return
		}

		if submissionResult.HasSubmitted {
			log.Printf("[SNBT Goal Tracker] User %s has already submitted the form (%d submissions)",
				input.Email, submissionResult.SubmissionCount)
			c.JSON(http.StatusConflict, gin.H{
				"success": false,
				"message": "You have already submitted this form",
				"count":   submissionResult.SubmissionCount,
			})
			return
		}

		log.Printf("[SNBT Goal Tracker] Inserting form data for user %s", input.Email)

		// Log form data summary
		log.Printf("[SNBT Goal Tracker] Form data summary - Penalaran Umum: %d, Pengetahuan Kuantitatif: %d, Pengetahuan & Pemahaman Umum: %d, Pemahaman Bacaan & Menulis: %d, Literasi Bahasa Indonesia: %d, Literasi Bahasa Inggris: %d, Penalaran Matematika: %d, Passed: %v, Felt Helped: %v, Rating: %d, Contact Consent: %v",
			input.PenalaranUmum,
			input.PengetahuanKuantitatif,
			input.PengetahuanPemahamanUmum,
			input.PemahamanBacaanMenulis,
			input.LiterasiBahasaIndonesia,
			input.LiterasiBahasaInggris,
			input.PenalaranMatematika,
			input.PassedSnbt,
			input.FeltHelped,
			input.HelpfulnessRating,
			input.ContactConsent)

		// Sanitize string inputs using bluemonday to prevent XSS
		p := bluemonday.UGCPolicy()
		sanitizedName := p.Sanitize(input.Name)
		sanitizedEmail := p.Sanitize(input.Email)
		sanitizedMostHelpfulAspect := p.Sanitize(input.MostHelpfulAspect)
		sanitizedImprovementSuggestions := p.Sanitize(input.ImprovementSuggestions)
		sanitizedPhoneNumber := p.Sanitize(input.PhoneNumber)

		log.Printf("[SNBT Goal Tracker] Sanitized input data for XSS prevention")

		// Prepare the insert query
		insertQuery := `
			INSERT INTO snbt_goal_tracker (
				name, email, penalaran_umum, pengetahuan_kuantitatif, pengetahuan_pemahaman_umum,
				pemahaman_bacaan_menulis, literasi_bahasa_indonesia, literasi_bahasa_inggris,
				penalaran_matematika, passed_snbt, felt_helped, helpfulness_rating,
				most_helpful_aspect, improvement_suggestions, contact_consent, phone_number
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16
			) RETURNING id, email, created_at`

		// Prepare the arguments with sanitized values
		args := []interface{}{
			sanitizedName,
			sanitizedEmail,
			input.PenalaranUmum,
			input.PengetahuanKuantitatif,
			input.PengetahuanPemahamanUmum,
			input.PemahamanBacaanMenulis,
			input.LiterasiBahasaIndonesia,
			input.LiterasiBahasaInggris,
			input.PenalaranMatematika,
			input.PassedSnbt,
			input.FeltHelped,
			input.HelpfulnessRating,
			sanitizedMostHelpfulAspect,
			sanitizedImprovementSuggestions,
			input.ContactConsent,
			sanitizedPhoneNumber,
		}

		// Insert the form data
		var result SnbtGoalTrackerResult
		err = tx.QueryRowxContext(ctx, insertQuery, args...).StructScan(&result)

		if err != nil {
			log.Printf("[SNBT Goal Tracker] Error inserting SNBT goal tracker data for %s: %v", input.Email, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Failed to save form data",
			})
			return
		}

		// Commit the transaction
		if err := tx.Commit(); err != nil {
			log.Printf("[SNBT Goal Tracker] Error committing transaction for %s: %v", input.Email, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Failed to commit transaction",
			})
			return
		}

		// Set tx to nil to prevent rollback in defer function
		tx = nil

		log.Printf("[SNBT Goal Tracker] Form submitted successfully for user %s (ID: %d)", input.Email, result.ID)

		c.JSON(http.StatusCreated, gin.H{
			"success": true,
			"message": "Form submitted successfully",
			"data":    result,
		})
	}
}

// checkSnbtGoalTrackerSubmission checks if a user has already submitted the form
func checkSnbtGoalTrackerSubmission(dbx *sqlx.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		email := c.Query("email")
		if email == "" {
			log.Printf("[SNBT Goal Tracker] Submission check failed: Email is required")
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "Email is required",
			})
			return
		}

		// Sanitize email input using bluemonday to prevent XSS
		p := bluemonday.UGCPolicy()
		sanitizedEmail := p.Sanitize(email)

		log.Printf("[SNBT Goal Tracker] Checking if user %s has already submitted the form", sanitizedEmail)

		// Create a context with timeout for database operation
		ctx, cancel := context.WithTimeout(c.Request.Context(), 2*time.Second)
		defer cancel()

		// Optimized query to get both submission status and count in one query
		query := `
			SELECT
				EXISTS(SELECT 1 FROM snbt_goal_tracker WHERE email = $1 LIMIT 1) as has_submitted,
				(SELECT COUNT(*) FROM snbt_goal_tracker WHERE email = $1) as submission_count
		`

		var result struct {
			HasSubmitted    bool `db:"has_submitted"`
			SubmissionCount int  `db:"submission_count"`
		}

		err := dbx.GetContext(ctx, &result, query, sanitizedEmail)
		if err != nil {
			log.Printf("[SNBT Goal Tracker] Error checking SNBT goal tracker submission for %s: %v", sanitizedEmail, err)
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "Failed to check submission status",
			})
			return
		}

		if result.HasSubmitted {
			log.Printf("[SNBT Goal Tracker] User %s has already submitted the form (%d submissions)", sanitizedEmail, result.SubmissionCount)
		} else {
			log.Printf("[SNBT Goal Tracker] User %s has not submitted the form yet", sanitizedEmail)
		}

		c.JSON(http.StatusOK, gin.H{
			"success":          true,
			"submitted":        result.HasSubmitted,
			"count":            result.SubmissionCount,
			"submission_count": result.SubmissionCount, // Added for consistency with eligibility endpoint
		})
	}
}
