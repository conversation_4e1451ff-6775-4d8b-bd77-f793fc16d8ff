{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_aef642._.js", "server/edge/chunks/node_modules_@auth_core_be3554._.js", "server/edge/chunks/0450b_jose_dist_webapi_14a220._.js", "server/edge/chunks/node_modules_@redis_client_dist_4f3922._.js", "server/edge/chunks/node_modules_b180e7._.js", "server/edge/chunks/[root of the server]__224f0e._.js", "server/edge/chunks/edge-wrapper_38784e.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!.+\\.[\\w]+$|_next).*){(\\\\.json)}?", "originalSource": "/((?!.+\\.[\\w]+$|_next).*)"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(/?index|/?index\\\\.json)?", "originalSource": "/"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/(api|trpc)(.*){(\\\\.json)}?", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "FlVjg73fjpv3w5tdqfrccxtpQm8KNV+TXJqHgl4Nc0g=", "__NEXT_PREVIEW_MODE_ID": "bcf2248b2fc9a4f1adf2328c85213699", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2ff769ef47d2aaa05e6933fd24ce9797219e897401e8cd7c046d0dfa66691ac8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "da2cc1b398c69b52ad1508501f69bf58a98dac7da97a9b135634a93ce82246a2"}}}, "instrumentation": null, "functions": {}}