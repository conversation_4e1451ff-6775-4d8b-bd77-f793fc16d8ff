"use client";

import React, { useState, useEffect } from "react";
import {
  Input,
  Button,
  RadioGroup,
  Radio,
  Textarea,
  Checkbox,
  Slider,
  Card,
  CardBody,
  Divider
} from "@heroui/react";

import { SnbtGoalTrackerFormData, FormErrors } from "./actions";

interface SnbtGoalTrackerFormProps {
  initialData?: Partial<SnbtGoalTrackerFormData>;
  onFormDataChange: (data: Partial<SnbtGoalTrackerFormData>) => void;
  onComplete: () => void;
  onDecline: () => void;
  clearError: (field: string) => void;
  formErrors: FormErrors;
  isSubmitting: boolean;
  userName?: string;
  userEmail?: string;
}

export function SnbtGoalTrackerForm({
  initialData = {},
  onFormDataChange,
  onComplete,
  onDecline,
  clearError,
  formErrors,
  isSubmitting,
  userName = "",
  userEmail = ""
}: SnbtGoalTrackerFormProps) {
  const [formData, setFormData] = useState<Partial<SnbtGoalTrackerFormData>>({
    name: userName || "",
    email: userEmail || "",
    penalaranUmum: 0,
    pengetahuanKuantitatif: 0,
    pengetahuanPemahamanUmum: 0,
    pemahamanBacaanMenulis: 0,
    literasiBahasaIndonesia: 0,
    literasiBahasaInggris: 0,
    penalaranMatematika: 0,
    passedSnbt: false,
    feltHelped: false,
    helpfulnessRating: 5,
    mostHelpfulAspect: "",
    improvementSuggestions: "",
    contactConsent: false,
    phoneNumber: "",
    ...initialData,
  });

  // Update form data when initialData changes
  useEffect(() => {
    if (userName && !formData.name) {
      setFormData(prev => ({ ...prev, name: userName }));
    }
    if (userEmail && !formData.email) {
      setFormData(prev => ({ ...prev, email: userEmail }));
    }
  }, [userName, userEmail, formData.name, formData.email]);

  // Notify parent component of form data changes
  useEffect(() => {
    onFormDataChange(formData);
  }, [formData, onFormDataChange]);

  const handleInputChange = (field: keyof SnbtGoalTrackerFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    clearError(field);
  };

  const handleDeclineClick = () => {
    // Set minimal required data for decline
    const declineData = {
      ...formData,
      name: formData.name || userName || "User",
      email: formData.email || userEmail || "",
      penalaranUmum: 0,
      pengetahuanKuantitatif: 0,
      pengetahuanPemahamanUmum: 0,
      pemahamanBacaanMenulis: 0,
      literasiBahasaIndonesia: 0,
      literasiBahasaInggris: 0,
      penalaranMatematika: 0,
      passedSnbt: false,
      feltHelped: false,
      helpfulnessRating: 1,
      mostHelpfulAspect: "Tidak mengikuti SNBT atau latihan yang berkaitan",
      improvementSuggestions: "-",
      contactConsent: false,
      phoneNumber: "-",
    };
    setFormData(declineData);
    onFormDataChange(declineData);
    onDecline();
  };

  const scoreFields = [
    { key: 'penalaranUmum', label: 'Penalaran Umum' },
    { key: 'pengetahuanKuantitatif', label: 'Pengetahuan Kuantitatif' },
    { key: 'pengetahuanPemahamanUmum', label: 'Pengetahuan dan Pemahaman Umum' },
    { key: 'pemahamanBacaanMenulis', label: 'Pemahaman Bacaan dan Menulis' },
    { key: 'literasiBahasaIndonesia', label: 'Literasi Bahasa Indonesia' },
    { key: 'literasiBahasaInggris', label: 'Literasi Bahasa Inggris' },
    { key: 'penalaranMatematika', label: 'Penalaran Matematika' }
  ];

  return (
    <div className="w-full max-w-4xl mx-auto p-4 sm:p-6 space-y-6">
      {/* Decline Button */}
      <div className="flex justify-center mb-6">
        <Button
          color="danger"
          variant="solid"
          onPress={handleDeclineClick}
          isDisabled={isSubmitting}
          className="w-full sm:w-auto px-6 py-2 text-sm sm:text-base"
        >
          Saya Tidak Mengikuti SNBT / Latihan yang Berkaitan
        </Button>
      </div>

      <Divider />

      {/* Personal Information */}
      <Card className="bg-white shadow-sm">
        <CardBody className="p-4 sm:p-6">
          <h3 className="text-lg font-semibold mb-4">Informasi Pribadi</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Nama Lengkap"
              placeholder="Masukkan nama lengkap Anda"
              value={formData.name || ""}
              onChange={(e) => handleInputChange('name', e.target.value)}
              isInvalid={!!formErrors.name}
              errorMessage={formErrors.name}
              isRequired
              className="w-full"
            />
            <Input
              label="Email"
              placeholder="Masukkan email Anda"
              type="email"
              value={formData.email || ""}
              onChange={(e) => handleInputChange('email', e.target.value)}
              isInvalid={!!formErrors.email}
              errorMessage={formErrors.email}
              isRequired
              className="w-full"
            />
          </div>
        </CardBody>
      </Card>

      {/* SNBT Scores */}
      <Card className="bg-white shadow-sm">
        <CardBody className="p-4 sm:p-6">
          <h3 className="text-lg font-semibold mb-4">Skor SNBT 2025 Anda</h3>
          <p className="text-sm text-gray-600 mb-6">
            Masukkan skor yang Anda peroleh untuk setiap komponen SNBT 2025
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {scoreFields.map(({ key, label }) => (
              <Input
                key={key}
                label={label}
                placeholder="0"
                type="number"
                min="0"
                value={formData[key as keyof SnbtGoalTrackerFormData]?.toString() || "0"}
                onChange={(e) => handleInputChange(key as keyof SnbtGoalTrackerFormData, parseInt(e.target.value) || 0)}
                isInvalid={!!formErrors[key]}
                errorMessage={formErrors[key]}
                isRequired
                className="w-full"
              />
            ))}
          </div>
        </CardBody>
      </Card>

      {/* SNBT Result */}
      <Card className="bg-white shadow-sm">
        <CardBody className="p-4 sm:p-6">
          <h3 className="text-lg font-semibold mb-4">Hasil SNBT</h3>
          <RadioGroup
            label="Apakah Anda lulus SNBT 2025?"
            value={formData.passedSnbt ? "true" : "false"}
            onValueChange={(value) => handleInputChange('passedSnbt', value === "true")}
            className="mb-4"
          >
            <Radio value="true">Ya, saya lulus SNBT 2025</Radio>
            <Radio value="false">Tidak, saya tidak lulus SNBT 2025</Radio>
          </RadioGroup>
        </CardBody>
      </Card>

      {/* Terang AI Feedback */}
      <Card className="bg-white shadow-sm">
        <CardBody className="p-4 sm:p-6">
          <h3 className="text-lg font-semibold mb-4">Feedback tentang Terang AI</h3>
          
          <RadioGroup
            label="Apakah Terang AI membantu Anda dalam persiapan SNBT?"
            value={formData.feltHelped ? "true" : "false"}
            onValueChange={(value) => handleInputChange('feltHelped', value === "true")}
            className="mb-6"
          >
            <Radio value="true">Ya, sangat membantu</Radio>
            <Radio value="false">Tidak terlalu membantu</Radio>
          </RadioGroup>

          <div className="mb-6">
            <label htmlFor="helpfulnessRating" className="block text-sm font-medium mb-2">
              Seberapa Terbantu Terang AI untuk membantu persiapan proses ujian? (1-10)
            </label>
            <div className="flex flex-col sm:flex-row items-center gap-2">
              <div className="flex items-center gap-2 w-full">
                <span className="text-sm font-medium">1</span>
                <Input
                  id="helpfulnessRating"
                  type="range"
                  min="1"
                  max="10"
                  value={formData.helpfulnessRating !== undefined ? formData.helpfulnessRating.toString() : '5'}
                  onChange={(e) => {
                    const value = e.target.value === '' ? 5 : parseInt(e.target.value);
                    handleInputChange("helpfulnessRating", value);
                  }}
                  className="flex-1"
                />
                <span className="text-sm font-medium">10</span>
              </div>
              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary text-white font-bold">
                {formData.helpfulnessRating || 5}
              </div>
            </div>
            {formErrors.helpfulnessRating && (
              <p className="text-danger text-sm mt-1">{formErrors.helpfulnessRating}</p>
            )}
          </div>

          <Textarea
            label="Aspek mana yang paling membantu?"
            placeholder="Ceritakan aspek Terang AI yang paling membantu dalam persiapan SNBT Anda..."
            value={formData.mostHelpfulAspect || ""}
            onChange={(e) => handleInputChange('mostHelpfulAspect', e.target.value)}
            className="mb-4"
            minRows={3}
          />

          <Textarea
            label="Saran untuk perbaikan"
            placeholder="Apa yang bisa kami tingkatkan untuk membantu persiapan SNBT yang lebih baik?"
            value={formData.improvementSuggestions || ""}
            onChange={(e) => handleInputChange('improvementSuggestions', e.target.value)}
            className="mb-4"
            minRows={3}
          />
        </CardBody>
      </Card>

      {/* Contact Consent */}
      <Card className="bg-white shadow-sm">
        <CardBody className="p-4 sm:p-6">
          <h3 className="text-lg font-semibold mb-4">Izin Kontak</h3>
          
          <Checkbox
            isSelected={formData.contactConsent || false}
            onValueChange={(checked) => handleInputChange('contactConsent', checked)}
            className="mb-4"
          >
            Saya setuju untuk dihubungi oleh tim Terang AI untuk follow-up atau penelitian lebih lanjut
          </Checkbox>

          {formData.contactConsent && (
            <Input
              label="Nomor Telepon"
              placeholder="Masukkan nomor telepon Anda"
              value={formData.phoneNumber || ""}
              onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
              isInvalid={!!formErrors.phoneNumber}
              errorMessage={formErrors.phoneNumber}
              className="w-full md:w-1/2"
            />
          )}
        </CardBody>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-center pt-6">
        <Button
          color="primary"
          size="lg"
          onPress={onComplete}
          isLoading={isSubmitting}
          isDisabled={isSubmitting}
          className="w-full sm:w-auto px-8 py-3 text-base font-medium"
        >
          {isSubmitting ? "Mengirim..." : "Kirim Survei"}
        </Button>
      </div>
    </div>
  );
}
