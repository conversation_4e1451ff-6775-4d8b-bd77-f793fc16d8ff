# SNBT Survey Feature Documentation

## Overview
The SNBT Survey feature allows users to submit feedback about their SNBT 2025 exam experience and how Terang AI helped them prepare. This feature is similar to the LPDP survey but specifically designed for SNBT (Seleksi Nasional Berdasarkan Tes) exam preparation.

## Features

### 1. Standalone Survey Page
- **URL**: `/snbt-survey`
- **Purpose**: Accessible survey page that can be shared via email or direct links
- **Authentication**: Optional (supports both logged-in and anonymous users)
- **Auto-fill**: Automatically fills user data if available from session or URL parameters

### 2. SNBT-Specific Score Components
The survey captures scores for all 7 SNBT 2025 components:
- Penalaran Umum
- Pengetahuan Kuantitatif
- Pen<PERSON>ahuan dan <PERSON>
- <PERSON><PERSON>haman <PERSON> dan <PERSON>
- Literasi Bahasa Indonesia
- Literasi Bahasa Inggris
- Penalaran Matematika

### 3. Feedback Collection
- SNBT exam result (passed/failed)
- Helpfulness of Terang AI (yes/no)
- Satisfaction rating (1-10 scale)
- Most helpful aspects (free text)
- Improvement suggestions (free text)
- Contact consent and phone number

### 4. Decline Option
- Red button at the top: "Saya Tidak Mengikuti SNBT / La<PERSON>han yang Berkaitan"
- Auto-fills form with minimal data and submits
- Prevents users from having to fill out irrelevant information

## Technical Implementation

### Backend (Go)
**File**: `backend-service/app/custom/snbt_goal_tracker.go`

#### API Endpoints
- `POST /v0/snbt-goal-tracker` - Submit survey data
- `GET /v0/snbt-goal-tracker/check-submission?email=<email>` - Check if user already submitted

#### Database Table
**Table**: `snbt_goal_tracker`
```sql
CREATE TABLE snbt_goal_tracker (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    penalaran_umum INTEGER NOT NULL,
    pengetahuan_kuantitatif INTEGER NOT NULL,
    pengetahuan_pemahaman_umum INTEGER NOT NULL,
    pemahaman_bacaan_menulis INTEGER NOT NULL,
    literasi_bahasa_indonesia INTEGER NOT NULL,
    literasi_bahasa_inggris INTEGER NOT NULL,
    penalaran_matematika INTEGER NOT NULL,
    passed_snbt BOOLEAN NOT NULL,
    felt_helped BOOLEAN NOT NULL,
    helpfulness_rating INTEGER NOT NULL CHECK (helpfulness_rating >= 1 AND helpfulness_rating <= 10),
    most_helpful_aspect TEXT,
    improvement_suggestions TEXT,
    contact_consent BOOLEAN NOT NULL DEFAULT FALSE,
    phone_number VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### Key Features
- **XSS Protection**: Uses bluemonday to sanitize all string inputs
- **Duplicate Prevention**: Unique constraint on email prevents multiple submissions
- **Transaction Safety**: All database operations wrapped in transactions
- **Comprehensive Logging**: Detailed logging for debugging and monitoring
- **Input Validation**: Server-side validation for all required fields and data types

### Frontend (React/Next.js)
**Files**:
- `terang-web-ui/app/snbt-survey/page.tsx` - Main survey page
- `terang-web-ui/components/snbt-goal-tracker/form.tsx` - Survey form component
- `terang-web-ui/components/snbt-goal-tracker/actions.ts` - Server actions for API calls

#### Key Features
- **Responsive Design**: Works on both mobile and desktop
- **Auto-fill Support**: Automatically fills user data from session or URL parameters
- **Real-time Validation**: Client-side validation with error messages
- **Loading States**: Proper loading indicators during submission
- **Success/Error Handling**: Toast notifications and success screens
- **Accessibility**: Proper form labels and ARIA attributes

## Usage Examples

### 1. Direct Access
```
https://terang.ai/snbt-survey
```

### 2. Pre-filled with User Email
```
https://terang.ai/snbt-survey?email=<EMAIL>
```

### 3. Email Campaign Integration
The survey can be integrated into email campaigns with personalized links:
```html
<a href="https://terang.ai/snbt-survey?email={{user.email}}">
  Isi Survei SNBT 2025
</a>
```

## Form Validation

### Required Fields
- Name (string, non-empty)
- Email (valid email format)
- All 7 SNBT score components (non-negative integers)
- Helpfulness rating (1-10)

### Optional Fields
- Most helpful aspect (text)
- Improvement suggestions (text)
- Phone number (required only if contact consent is given)

### Business Rules
- Each email can only submit once (enforced at database level)
- All score fields must be non-negative numbers
- Helpfulness rating must be between 1-10
- Phone number is required if contact consent is checked

## Security Features

### XSS Prevention
- All string inputs sanitized using bluemonday policy
- Consistent with other survey implementations in the system

### API Security
- Requires valid API key in `X-API-KEY` header
- Input validation on both client and server side
- SQL injection prevention through parameterized queries

### Data Privacy
- Contact consent explicitly requested
- Phone number only collected with consent
- Email used for duplicate prevention only

## Monitoring and Analytics

### Logging
- Comprehensive logging for all operations
- Includes user email (sanitized) for tracking
- Error logging for debugging
- Performance metrics for database operations

### Database Indexes
- Email index for fast duplicate checking
- Unique constraint on email for data integrity
- Timestamps for analytics and reporting

## Integration Points

### Email System
- Can be integrated with Asynq email workers for automated campaigns
- Supports personalized email links with pre-filled data

### User Management
- Integrates with existing user authentication system
- Supports both authenticated and anonymous submissions

### Analytics
- Survey data can be used for product improvement insights
- Helps track effectiveness of SNBT preparation features

## Deployment Notes

### Database Migration
Run migration `000036_snbt_goal_tracker` to create the required table:
```bash
# Migration files created:
# - backend-service/migrations/files/000036_snbt_goal_tracker.up.sql
# - backend-service/migrations/files/000036_snbt_goal_tracker.down.sql
```

### Route Registration
Routes are automatically registered in `backend-service/app/app.go`:
```go
// snbt goal tracker
custom.RegisterSnbtGoalTrackerRoutes(r, a.DBx)
```

### Frontend Deployment
- Survey page available at `/snbt-survey`
- Added to sitemap for SEO
- No additional configuration required

## Future Enhancements

### Potential Improvements
1. **Analytics Dashboard**: Admin interface to view survey responses
2. **Export Functionality**: CSV/Excel export of survey data
3. **Follow-up Surveys**: Automated follow-up surveys based on responses
4. **Integration with CRM**: Sync contact consent data with marketing tools
5. **A/B Testing**: Different survey versions for optimization

### Scalability Considerations
- Database indexes ensure fast queries even with large datasets
- Stateless design allows horizontal scaling
- Caching can be added for frequently accessed data

## Troubleshooting

### Common Issues
1. **Duplicate Submission Error**: User already submitted - check email uniqueness
2. **Validation Errors**: Ensure all required fields are properly filled
3. **API Key Issues**: Verify backend API key configuration
4. **Database Connection**: Check database connectivity and migration status

### Debug Information
- Check browser console for client-side errors
- Review backend logs for server-side issues
- Verify API endpoints are accessible
- Confirm database table exists and has proper structure
