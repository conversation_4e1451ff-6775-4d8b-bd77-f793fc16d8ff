{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GAWEAN/TERANG/terang-web-ui/app/snbt-survey/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/snbt-survey/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/snbt-survey/page.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA"}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GAWEAN/TERANG/terang-web-ui/app/snbt-survey/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/app/snbt-survey/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/snbt-survey/page.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwQ,GACrS,sCACA"}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GAWEAN/TERANG/terang-web-ui/components/snbt-goal-tracker/actions.ts"], "sourcesContent": ["\"use server\";\n\nimport { auth } from \"@/auth\";\n\nexport interface SnbtGoalTrackerFormData {\n  name: string;\n  email: string;\n  penalaranUmum: number;\n  pengetahuanKuantitatif: number;\n  pengetahuanPemahamanUmum: number;\n  pemahamanBacaanMenulis: number;\n  literasiBahasaIndonesia: number;\n  literasiBahasaInggris: number;\n  penalaranMatematika: number;\n  passedSnbt: boolean;\n  feltHelped: boolean;\n  helpfulnessRating: number;\n  mostHelpfulAspect: string;\n  improvementSuggestions: string;\n  contactConsent: boolean;\n  phoneNumber: string;\n}\n\nexport interface FormErrors {\n  [key: string]: string;\n}\n\nexport interface SubmissionResult {\n  success: boolean;\n  message?: string;\n  data?: any;\n}\n\n// Validate SNBT goal tracker form data\nexport async function validateSnbtGoalTrackerForm(data: SnbtGoalTrackerFormData): Promise<FormErrors> {\n  const errors: FormErrors = {};\n\n  // Validate required fields\n  if (!data.name || data.name.trim().length === 0) {\n    errors.name = \"Nama harus diisi\";\n  }\n\n  if (!data.email || data.email.trim().length === 0) {\n    errors.email = \"Email harus diisi\";\n  } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.email)) {\n    errors.email = \"Format email tidak valid\";\n  }\n\n  // Validate score fields (should be non-negative numbers)\n  const scoreFields = [\n    { field: 'penalaranUmum', label: 'Penalaran Umum' },\n    { field: 'pengetahuanKuantitatif', label: 'Pengetahuan Kuantitatif' },\n    { field: 'pengetahuanPemahamanUmum', label: 'Pengetahuan dan Pemahaman Umum' },\n    { field: 'pemahamanBacaanMenulis', label: 'Pemahaman Bacaan dan Menulis' },\n    { field: 'literasiBahasaIndonesia', label: 'Literasi Bahasa Indonesia' },\n    { field: 'literasiBahasaInggris', label: 'Literasi Bahasa Inggris' },\n    { field: 'penalaranMatematika', label: 'Penalaran Matematika' }\n  ];\n\n  scoreFields.forEach(({ field, label }) => {\n    const value = data[field as keyof SnbtGoalTrackerFormData] as number;\n    if (value === undefined || value === null || isNaN(value) || value < 0) {\n      errors[field] = `${label} harus berupa angka yang valid (minimal 0)`;\n    }\n  });\n\n  // Validate helpfulness rating\n  if (data.helpfulnessRating === undefined || data.helpfulnessRating === null || \n      isNaN(data.helpfulnessRating) || data.helpfulnessRating < 1 || data.helpfulnessRating > 10) {\n    errors.helpfulnessRating = \"Rating kepuasan harus antara 1-10\";\n  }\n\n  // Validate phone number if contact consent is given\n  if (data.contactConsent && (!data.phoneNumber || data.phoneNumber.trim().length === 0)) {\n    errors.phoneNumber = \"Nomor telepon harus diisi jika Anda setuju untuk dihubungi\";\n  }\n\n  return errors;\n}\n\n// Submit SNBT goal tracker form\nexport async function submitSnbtGoalTrackerForm(data: SnbtGoalTrackerFormData): Promise<SubmissionResult> {\n  try {\n    console.log(\"[SNBT Goal Tracker Actions] Submitting form data:\", data);\n\n    const response = await fetch(`${process.env.BACKEND_BASE_URL}/v0/snbt-goal-tracker`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-API-KEY': process.env.BACKEND_API_KEY as string,\n      },\n      body: JSON.stringify({\n        name: data.name,\n        email: data.email,\n        penalaran_umum: data.penalaranUmum,\n        pengetahuan_kuantitatif: data.pengetahuanKuantitatif,\n        pengetahuan_pemahaman_umum: data.pengetahuanPemahamanUmum,\n        pemahaman_bacaan_menulis: data.pemahamanBacaanMenulis,\n        literasi_bahasa_indonesia: data.literasiBahasaIndonesia,\n        literasi_bahasa_inggris: data.literasiBahasaInggris,\n        penalaran_matematika: data.penalaranMatematika,\n        passed_snbt: data.passedSnbt,\n        felt_helped: data.feltHelped,\n        helpfulness_rating: data.helpfulnessRating,\n        most_helpful_aspect: data.mostHelpfulAspect,\n        improvement_suggestions: data.improvementSuggestions,\n        contact_consent: data.contactConsent,\n        phone_number: data.phoneNumber,\n      }),\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      console.error(\"[SNBT Goal Tracker Actions] Submission failed:\", result);\n      return {\n        success: false,\n        message: result.message || `HTTP error! status: ${response.status}`,\n      };\n    }\n\n    console.log(\"[SNBT Goal Tracker Actions] Submission successful:\", result);\n    return {\n      success: true,\n      message: \"Form berhasil dikirim\",\n      data: result.data,\n    };\n  } catch (error) {\n    console.error(\"[SNBT Goal Tracker Actions] Error submitting form:\", error);\n    return {\n      success: false,\n      message: error instanceof Error ? error.message : \"Terjadi kesalahan tidak diketahui\",\n    };\n  }\n}\n\n// Check if user has already submitted SNBT goal tracker form\nexport async function checkSnbtGoalTrackerSubmission(email?: string): Promise<boolean> {\n  try {\n    // If no email provided, try to get from session\n    let userEmail = email;\n    if (!userEmail) {\n      const session = await auth();\n      userEmail = session?.user?.email || \"\";\n    }\n\n    if (!userEmail) {\n      console.log(\"[SNBT Goal Tracker Actions] No email available for submission check\");\n      return false;\n    }\n\n    console.log(\"[SNBT Goal Tracker Actions] Checking submission status for:\", userEmail);\n\n    const response = await fetch(\n      `${process.env.BACKEND_BASE_URL}/v0/snbt-goal-tracker/check-submission?email=${encodeURIComponent(userEmail)}`,\n      {\n        method: 'GET',\n        headers: {\n          'X-API-KEY': process.env.BACKEND_API_KEY as string,\n        },\n      }\n    );\n\n    if (!response.ok) {\n      console.error(\"[SNBT Goal Tracker Actions] Failed to check submission status:\", response.status);\n      return false;\n    }\n\n    const result = await response.json();\n    console.log(\"[SNBT Goal Tracker Actions] Submission check result:\", result);\n\n    return result.submitted || false;\n  } catch (error) {\n    console.error(\"[SNBT Goal Tracker Actions] Error checking submission:\", error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;;;;;AAgCO,eAAe,uCAAyB,GAAzB,4BAA4B,IAA6B;IAC7E,MAAM,SAAqB,CAAC;IAE5B,2BAA2B;IAC3B,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;QAC/C,OAAO,IAAI,GAAG;IAChB;IAEA,IAAI,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,IAAI,GAAG,MAAM,KAAK,GAAG;QACjD,OAAO,KAAK,GAAG;IACjB,OAAO,IAAI,CAAC,6BAA6B,IAAI,CAAC,KAAK,KAAK,GAAG;QACzD,OAAO,KAAK,GAAG;IACjB;IAEA,yDAAyD;IACzD,MAAM,cAAc;QAClB;YAAE,OAAO;YAAiB,OAAO;QAAiB;QAClD;YAAE,OAAO;YAA0B,OAAO;QAA0B;QACpE;YAAE,OAAO;YAA4B,OAAO;QAAiC;QAC7E;YAAE,OAAO;YAA0B,OAAO;QAA+B;QACzE;YAAE,OAAO;YAA2B,OAAO;QAA4B;QACvE;YAAE,OAAO;YAAyB,OAAO;QAA0B;QACnE;YAAE,OAAO;YAAuB,OAAO;QAAuB;KAC/D;IAED,YAAY,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE;QACnC,MAAM,QAAQ,IAAI,CAAC,MAAuC;QAC1D,IAAI,UAAU,aAAa,UAAU,QAAQ,MAAM,UAAU,QAAQ,GAAG;YACtE,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,0CAA0C,CAAC;QACtE;IACF;IAEA,8BAA8B;IAC9B,IAAI,KAAK,iBAAiB,KAAK,aAAa,KAAK,iBAAiB,KAAK,QACnE,MAAM,KAAK,iBAAiB,KAAK,KAAK,iBAAiB,GAAG,KAAK,KAAK,iBAAiB,GAAG,IAAI;QAC9F,OAAO,iBAAiB,GAAG;IAC7B;IAEA,oDAAoD;IACpD,IAAI,KAAK,cAAc,IAAI,CAAC,CAAC,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,IAAI,GAAG,MAAM,KAAK,CAAC,GAAG;QACtF,OAAO,WAAW,GAAG;IACvB;IAEA,OAAO;AACT;AAGO,eAAe,uCAAuB,GAAvB,0BAA0B,IAA6B;IAC3E,IAAI;QACF,QAAQ,GAAG,CAAC,qDAAqD;QAEjE,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,GAAG,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,EAAE;YACnF,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,aAAa,QAAQ,GAAG,CAAC,eAAe;YAC1C;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;gBACjB,gBAAgB,KAAK,aAAa;gBAClC,yBAAyB,KAAK,sBAAsB;gBACpD,4BAA4B,KAAK,wBAAwB;gBACzD,0BAA0B,KAAK,sBAAsB;gBACrD,2BAA2B,KAAK,uBAAuB;gBACvD,yBAAyB,KAAK,qBAAqB;gBACnD,sBAAsB,KAAK,mBAAmB;gBAC9C,aAAa,KAAK,UAAU;gBAC5B,aAAa,KAAK,UAAU;gBAC5B,oBAAoB,KAAK,iBAAiB;gBAC1C,qBAAqB,KAAK,iBAAiB;gBAC3C,yBAAyB,KAAK,sBAAsB;gBACpD,iBAAiB,KAAK,cAAc;gBACpC,cAAc,KAAK,WAAW;YAChC;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,kDAAkD;YAChE,OAAO;gBACL,SAAS;gBACT,SAAS,OAAO,OAAO,IAAI,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YACrE;QACF;QAEA,QAAQ,GAAG,CAAC,sDAAsD;QAClE,OAAO;YACL,SAAS;YACT,SAAS;YACT,MAAM,OAAO,IAAI;QACnB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sDAAsD;QACpE,OAAO;YACL,SAAS;YACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD;IACF;AACF;AAGO,eAAe,uCAA4B,GAA5B,+BAA+B,KAAc;IACjE,IAAI;QACF,gDAAgD;QAChD,IAAI,YAAY;QAChB,IAAI,CAAC,WAAW;YACd,MAAM,UAAU,MAAM,CAAA,GAAA,oGAAA,CAAA,OAAI,AAAD;YACzB,YAAY,SAAS,MAAM,SAAS;QACtC;QAEA,IAAI,CAAC,WAAW;YACd,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,+DAA+D;QAE3E,MAAM,WAAW,MAAM,MACrB,GAAG,QAAQ,GAAG,CAAC,gBAAgB,CAAC,6CAA6C,EAAE,mBAAmB,YAAY,EAC9G;YACE,QAAQ;YACR,SAAS;gBACP,aAAa,QAAQ,GAAG,CAAC,eAAe;YAC1C;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,kEAAkE,SAAS,MAAM;YAC/F,OAAO;QACT;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,wDAAwD;QAEpE,OAAO,OAAO,SAAS,IAAI;IAC7B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0DAA0D;QACxE,OAAO;IACT;AACF;;;IA9IsB;IA+CA;IAwDA;;AAvGA,+OAAA;AA+CA,+OAAA;AAwDA,+OAAA"}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GAWEAN/TERANG/terang-web-ui/app/snbt-survey/actions.ts"], "sourcesContent": ["\"use server\";\n\nimport { auth } from \"@/auth\";\n\nexport interface UserData {\n  name: string;\n  email: string;\n}\n\nexport async function getUserData(email?: string): Promise<UserData | null> {\n  try {\n    // If email is provided (from URL params), use it directly\n    if (email) {\n      console.log(\"[SNBT Survey] Using provided email:\", email);\n      \n      // Try to fetch user data from backend\n      const response = await fetch(\n        `${process.env.BACKEND_BASE_URL}/v0/users/profile?email=${encodeURIComponent(email)}`,\n        {\n          method: 'GET',\n          headers: {\n            'X-API-KEY': process.env.BACKEND_API_KEY as string,\n          },\n        }\n      );\n\n      if (response.ok) {\n        const userData = await response.json();\n        console.log(\"[SNBT Survey] User data fetched from backend:\", userData);\n        return {\n          name: userData.name || \"\",\n          email: userData.email || email,\n        };\n      } else {\n        console.log(\"[SNBT Survey] User not found in backend, using email only\");\n        return {\n          name: \"\",\n          email: email,\n        };\n      }\n    }\n\n    // If no email provided, try to get from session\n    const session = await auth();\n    if (session?.user) {\n      console.log(\"[SNBT Survey] Using session data:\", session.user);\n      return {\n        name: session.user.name || \"\",\n        email: session.user.email || \"\",\n      };\n    }\n\n    console.log(\"[SNBT Survey] No user data available\");\n    return null;\n  } catch (error) {\n    console.error(\"[SNBT Survey] Error getting user data:\", error);\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;;;AAOO,eAAe,uCAAS,GAAT,YAAY,KAAc;IAC9C,IAAI;QACF,0DAA0D;QAC1D,IAAI,OAAO;YACT,QAAQ,GAAG,CAAC,uCAAuC;YAEnD,sCAAsC;YACtC,MAAM,WAAW,MAAM,MACrB,GAAG,QAAQ,GAAG,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,mBAAmB,QAAQ,EACrF;gBACE,QAAQ;gBACR,SAAS;oBACP,aAAa,QAAQ,GAAG,CAAC,eAAe;gBAC1C;YACF;YAGF,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,WAAW,MAAM,SAAS,IAAI;gBACpC,QAAQ,GAAG,CAAC,iDAAiD;gBAC7D,OAAO;oBACL,MAAM,SAAS,IAAI,IAAI;oBACvB,OAAO,SAAS,KAAK,IAAI;gBAC3B;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,OAAO;oBACL,MAAM;oBACN,OAAO;gBACT;YACF;QACF;QAEA,gDAAgD;QAChD,MAAM,UAAU,MAAM,CAAA,GAAA,oGAAA,CAAA,OAAI,AAAD;QACzB,IAAI,SAAS,MAAM;YACjB,QAAQ,GAAG,CAAC,qCAAqC,QAAQ,IAAI;YAC7D,OAAO;gBACL,MAAM,QAAQ,IAAI,CAAC,IAAI,IAAI;gBAC3B,OAAO,QAAQ,IAAI,CAAC,KAAK,IAAI;YAC/B;QACF;QAEA,QAAQ,GAAG,CAAC;QACZ,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0CAA0C;QACxD,OAAO;IACT;AACF;;;IAjDsB;;AAAA,+OAAA"}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GAWEAN/TERANG/terang-web-ui/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind'\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR'\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: any\ndeclare const __next_app_load_chunk__: any\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base'\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AA0BA,8BAA8B;AAzB9B,SAASA,kBAAkB,QAAQ,2DAA2D;AAAqC,EAAC;AAYpI,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;AAWtB,OAAO,MAAMG,eAAe;AAG5B,EAAC;;;;;;;;;AAED,cAAc,qCAAoC,sBAAA;AAElD,UAAA,kDAA4D;AAC5D,MAAA,CAAO,MAAMK;IAAAA;IAAAA,SAAc,IAAIX,mBAAmB;YAChDY,QAAAA;YAAAA,GAAY;YAAA;wBACVC,IAAAA;oBAAAA,CAAMZ,UAAUa;oBAAAA,OAAQ;yBACxBC,MAAM;8BACNC,IAAAA,CAAAA,GAAU;wBAAA,QAAA;4BAAA,IAAA;4BAAA;yBAAA;;uBACV,2CAA2C;;iBAC3CC,YAAY;sBACZC,IAAAA,CAAAA;YAAAA,CAAU;SAAA;;SACVC,UAAU,EAAE;UACd,QAAA,CAAA;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;UACAC,OAAAA;YAAAA,EAAU,EAAA;YAAA;SAAA;cACRC,OAAAA;YAAAA,IAAYnB;YAAAA;SAAAA;UACd,WAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,eAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0]}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}