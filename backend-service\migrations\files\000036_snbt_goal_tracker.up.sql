-- Create snbt_goal_tracker table
CREATE TABLE IF NOT EXISTS snbt_goal_tracker (
    id SERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    penalaran_umum INTEGER NOT NULL,
    pengetahuan_kuantitatif INTEGER NOT NULL,
    pengetahuan_pemahaman_umum INTEGER NOT NULL,
    pemahaman_bacaan_menulis INTEGER NOT NULL,
    literasi_bahasa_indonesia INTEGER NOT NULL,
    literasi_bahasa_inggris INTEGER NOT NULL,
    penalaran_matematika INTEGER NOT NULL,
    passed_snbt BOOLEAN NOT NULL,
    felt_helped BOOLEAN NOT NULL,
    helpfulness_rating INTEGER NOT NULL CHECK (helpfulness_rating >= 1 AND helpfulness_rating <= 10),
    most_helpful_aspect TEXT,
    improvement_suggestions TEXT,
    contact_consent BOOLEAN NOT NULL DEFAULT FALSE,
    phone_number VARCHAR(20),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    modified_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX idx_snbt_goal_tracker_email ON snbt_goal_tracker(email);
CREATE UNIQUE INDEX unique_snbt_goal_tracker_email ON snbt_goal_tracker(email);

-- Add comments to explain the fields
COMMENT ON TABLE snbt_goal_tracker IS 'Tracks user goals and feedback for SNBT 2025 exam preparation';
COMMENT ON COLUMN snbt_goal_tracker.penalaran_umum IS 'Penalaran Umum score in SNBT 2025';
COMMENT ON COLUMN snbt_goal_tracker.pengetahuan_kuantitatif IS 'Pengetahuan Kuantitatif score in SNBT 2025';
COMMENT ON COLUMN snbt_goal_tracker.pengetahuan_pemahaman_umum IS 'Pengetahuan dan Pemahaman Umum score in SNBT 2025';
COMMENT ON COLUMN snbt_goal_tracker.pemahaman_bacaan_menulis IS 'Pemahaman Bacaan dan Menulis score in SNBT 2025';
COMMENT ON COLUMN snbt_goal_tracker.literasi_bahasa_indonesia IS 'Literasi Bahasa Indonesia score in SNBT 2025';
COMMENT ON COLUMN snbt_goal_tracker.literasi_bahasa_inggris IS 'Literasi Bahasa Inggris score in SNBT 2025';
COMMENT ON COLUMN snbt_goal_tracker.penalaran_matematika IS 'Penalaran Matematika score in SNBT 2025';
COMMENT ON COLUMN snbt_goal_tracker.passed_snbt IS 'Whether the user passed SNBT 2025';
COMMENT ON COLUMN snbt_goal_tracker.felt_helped IS 'Whether the user felt helped in preparation for SNBT 2025';
COMMENT ON COLUMN snbt_goal_tracker.helpfulness_rating IS 'Rating of how helpful Terang AI was (1-10)';
COMMENT ON COLUMN snbt_goal_tracker.most_helpful_aspect IS 'What aspect the user found most helpful';
COMMENT ON COLUMN snbt_goal_tracker.improvement_suggestions IS 'User suggestions for features to improve';
COMMENT ON COLUMN snbt_goal_tracker.contact_consent IS 'Whether the user consents to being contacted';
COMMENT ON COLUMN snbt_goal_tracker.phone_number IS 'User phone number if consent is given';
