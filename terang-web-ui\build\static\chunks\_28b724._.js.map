{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GAWEAN/TERANG/terang-web-ui/components/lpdp-goal-tracker/form.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport {\r\n  Form,\r\n  Input,\r\n  Button,\r\n  Textarea,\r\n  Checkbox,\r\n} from \"@heroui/react\";\r\nimport { LpdpGoalTrackerFormData, FormErrors } from \"./actions\";\r\n\r\ninterface LpdpGoalTrackerFormProps {\r\n  initialData?: Partial<LpdpGoalTrackerFormData>;\r\n  formErrors?: FormErrors;\r\n  clearError?: (field: string) => void;\r\n  onFormDataChange?: (data: Partial<LpdpGoalTrackerFormData>) => void;\r\n  onComplete?: () => Promise<void>;\r\n  onDecline?: () => Promise<void>;\r\n  isSubmitting?: boolean;\r\n  userName?: string;\r\n  userEmail?: string;\r\n}\r\n\r\nexport function LpdpGoalTrackerForm({\r\n  initialData,\r\n  formErrors = {},\r\n  clearError,\r\n  onFormDataChange,\r\n  onComplete,\r\n  onDecline,\r\n  isSubmitting = false,\r\n  userName,\r\n  userEmail,\r\n}: LpdpGoalTrackerFormProps) {\r\n  const [formData, setFormData] = useState<Partial<LpdpGoalTrackerFormData>>({\r\n    name: userName || \"\",\r\n    email: userEmail || \"\",\r\n    helpfulnessRating: 5,\r\n    passedLpdpTbs: false,\r\n    feltHelped: false,\r\n    mostHelpfulAspect: \"\",\r\n    improvementSuggestions: \"\",\r\n    contactConsent: false,\r\n    phoneNumber: \"\",\r\n    ...(initialData || {}),\r\n  });\r\n\r\n  // Update form data when initialData changes\r\n  useEffect(() => {\r\n    if (initialData) {\r\n      setFormData((prev) => ({\r\n        ...prev,\r\n        ...initialData,\r\n      }));\r\n    }\r\n  }, [initialData]);\r\n\r\n  // Update form data when userName or userEmail changes\r\n  useEffect(() => {\r\n    if (userName) {\r\n      setFormData((prev) => ({\r\n        ...prev,\r\n        name: userName,\r\n      }));\r\n    }\r\n    if (userEmail) {\r\n      setFormData((prev) => ({\r\n        ...prev,\r\n        email: userEmail,\r\n      }));\r\n    }\r\n  }, [userName, userEmail]);\r\n\r\n  const handleInputChange = (field: keyof LpdpGoalTrackerFormData, value: any) => {\r\n    // Clear error for this field if it exists\r\n    if (clearError && formErrors[field]) {\r\n      clearError(field);\r\n    }\r\n\r\n    // Update form data\r\n    setFormData((prev) => {\r\n      const newData = { ...prev, [field]: value };\r\n\r\n      // Notify parent component of the change\r\n      if (onFormDataChange) {\r\n        onFormDataChange(newData);\r\n      }\r\n\r\n      return newData;\r\n    });\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (onComplete) {\r\n      await onComplete();\r\n    }\r\n  };\r\n\r\n  const handleDecline = async () => {\r\n    if (onDecline) {\r\n      await onDecline();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Form className=\"space-y-6 py-4 px-2 sm:px-4\" onSubmit={handleSubmit}>\r\n      {/* Centered decline button with improved mobile styling */}\r\n      <div className=\"flex justify-center items-center mb-6 w-full\">\r\n        <Button\r\n          type=\"button\"\r\n          color=\"danger\"\r\n          variant=\"solid\"\r\n          isLoading={isSubmitting}\r\n          isDisabled={isSubmitting}\r\n          size=\"lg\"\r\n          className=\"px-4 sm:px-6 py-4 sm:py-5 text-sm sm:text-base font-medium whitespace-normal text-center\"\r\n          style={{ width: \"280px\", maxWidth: \"100%\" }}\r\n          onPress={handleDecline}\r\n        >\r\n          Saya Tidak Mengikuti LPDP\r\n        </Button>\r\n      </div>\r\n      <div className=\"space-y-5\">\r\n        <div className=\"bg-white p-4 md:p-6 rounded-xl shadow-sm border\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"w-1 h-6 bg-primary rounded-full mr-3\"></div>\r\n            <h3 className=\"text-lg font-semibold\">Informasi Pribadi</h3>\r\n          </div>\r\n\r\n          <div className=\"space-y-4\">\r\n            <Input\r\n              label=\"Nama\"\r\n              placeholder=\"Masukkan nama lengkap\"\r\n              value={formData.name}\r\n              onChange={(e) => handleInputChange(\"name\", e.target.value)}\r\n              isInvalid={!!formErrors.name}\r\n              errorMessage={formErrors.name}\r\n              isRequired\r\n              classNames={{\r\n                base: \"max-w-full\",\r\n                inputWrapper: \"bg-white\"\r\n              }}\r\n            />\r\n\r\n            <Input\r\n              label=\"Email\"\r\n              placeholder=\"Masukkan email\"\r\n              value={formData.email}\r\n              onChange={(e) => handleInputChange(\"email\", e.target.value)}\r\n              isInvalid={!!formErrors.email}\r\n              errorMessage={formErrors.email}\r\n              isRequired\r\n              classNames={{\r\n                base: \"max-w-full\",\r\n                inputWrapper: \"bg-white\"\r\n              }}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white p-4 md:p-6 rounded-xl shadow-sm border\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"w-1 h-6 bg-primary rounded-full mr-3\"></div>\r\n            <h3 className=\"text-lg font-semibold\">Hasil TBS LPDP Batch 1</h3>\r\n          </div>\r\n\r\n          <div className=\"space-y-5\">\r\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4\">\r\n              <Input\r\n                type=\"number\"\r\n                label=\"Penalaran Verbal\"\r\n                placeholder=\"Masukkan nilai\"\r\n                value={formData.verbalReasoning !== undefined ? formData.verbalReasoning.toString() : ''}\r\n                onChange={(e) => {\r\n                  const value = e.target.value === '' ? undefined : parseInt(e.target.value);\r\n                  handleInputChange(\"verbalReasoning\", value);\r\n                }}\r\n                isInvalid={!!formErrors.verbalReasoning}\r\n                errorMessage={formErrors.verbalReasoning}\r\n                isRequired\r\n                classNames={{\r\n                  base: \"max-w-full\",\r\n                  inputWrapper: \"bg-white\"\r\n                }}\r\n              />\r\n\r\n              <Input\r\n                type=\"number\"\r\n                label=\"Penalaran Kuantitatif\"\r\n                placeholder=\"Masukkan nilai\"\r\n                value={formData.quantitativeReasoning !== undefined ? formData.quantitativeReasoning.toString() : ''}\r\n                onChange={(e) => {\r\n                  const value = e.target.value === '' ? undefined : parseInt(e.target.value);\r\n                  handleInputChange(\"quantitativeReasoning\", value);\r\n                }}\r\n                isInvalid={!!formErrors.quantitativeReasoning}\r\n                errorMessage={formErrors.quantitativeReasoning}\r\n                isRequired\r\n                classNames={{\r\n                  base: \"max-w-full\",\r\n                  inputWrapper: \"bg-white\"\r\n                }}\r\n              />\r\n\r\n              <Input\r\n                type=\"number\"\r\n                label=\"Pemecahan Masalah\"\r\n                placeholder=\"Masukkan nilai\"\r\n                value={formData.problemSolving !== undefined ? formData.problemSolving.toString() : ''}\r\n                onChange={(e) => {\r\n                  const value = e.target.value === '' ? undefined : parseInt(e.target.value);\r\n                  handleInputChange(\"problemSolving\", value);\r\n                }}\r\n                isInvalid={!!formErrors.problemSolving}\r\n                errorMessage={formErrors.problemSolving}\r\n                isRequired\r\n                classNames={{\r\n                  base: \"max-w-full\",\r\n                  inputWrapper: \"bg-white\"\r\n                }}\r\n              />\r\n            </div>\r\n\r\n            <div className=\"space-y-3\">\r\n              <fieldset>\r\n                <legend className=\"block text-sm font-medium\">\r\n                  Apakah Lulus TBS LPDP Batch 1? <span className=\"text-danger\">*</span>\r\n                </legend>\r\n                <div className=\"flex flex-wrap gap-3 mt-2\">\r\n                  <button\r\n                    type=\"button\"\r\n                    className={`flex items-center gap-2 p-3 border rounded-lg cursor-pointer transition-all ${formData.passedLpdpTbs ? 'border-primary bg-primary/10' : 'border-gray-200 hover:border-gray-300'}`}\r\n                    onClick={() => handleInputChange(\"passedLpdpTbs\", true)}\r\n                    aria-pressed={formData.passedLpdpTbs}\r\n                  >\r\n                    <div className={`w-5 h-5 rounded-full border flex items-center justify-center ${formData.passedLpdpTbs ? 'border-primary bg-primary' : 'border-gray-400'}`}>\r\n                      {formData.passedLpdpTbs && <span className=\"text-white text-xs\">✓</span>}\r\n                    </div>\r\n                    <span>Ya</span>\r\n                  </button>\r\n                  <button\r\n                    type=\"button\"\r\n                    className={`flex items-center gap-2 p-3 border rounded-lg cursor-pointer transition-all ${!formData.passedLpdpTbs ? 'border-primary bg-primary/10' : 'border-gray-200 hover:border-gray-300'}`}\r\n                    onClick={() => handleInputChange(\"passedLpdpTbs\", false)}\r\n                    aria-pressed={!formData.passedLpdpTbs}\r\n                  >\r\n                    <div className={`w-5 h-5 rounded-full border flex items-center justify-center ${!formData.passedLpdpTbs ? 'border-primary bg-primary' : 'border-gray-400'}`}>\r\n                      {!formData.passedLpdpTbs && <span className=\"text-white text-xs\">✓</span>}\r\n                    </div>\r\n                    <span>Tidak</span>\r\n                  </button>\r\n                </div>\r\n              </fieldset>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white p-4 md:p-6 rounded-xl shadow-sm border\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"w-1 h-6 bg-primary rounded-full mr-3\"></div>\r\n            <h3 className=\"text-lg font-semibold\">Evaluasi Terang AI</h3>\r\n          </div>\r\n\r\n          <div className=\"space-y-5\">\r\n            <div className=\"space-y-3\">\r\n              <fieldset>\r\n                <legend className=\"block text-sm font-medium\">\r\n                  Apakah Merasa Terbantu dalam persiapan TBS LPDP Batch 1? <span className=\"text-danger\">*</span>\r\n                </legend>\r\n                <div className=\"flex flex-wrap gap-3 mt-2\">\r\n                  <button\r\n                    type=\"button\"\r\n                    className={`flex items-center gap-2 p-3 border rounded-lg cursor-pointer transition-all ${formData.feltHelped ? 'border-primary bg-primary/10' : 'border-gray-200 hover:border-gray-300'}`}\r\n                    onClick={() => handleInputChange(\"feltHelped\", true)}\r\n                    aria-pressed={formData.feltHelped}\r\n                  >\r\n                    <div className={`w-5 h-5 rounded-full border flex items-center justify-center ${formData.feltHelped ? 'border-primary bg-primary' : 'border-gray-400'}`}>\r\n                      {formData.feltHelped && <span className=\"text-white text-xs\">✓</span>}\r\n                    </div>\r\n                    <span>Ya</span>\r\n                  </button>\r\n                  <button\r\n                    type=\"button\"\r\n                    className={`flex items-center gap-2 p-3 border rounded-lg cursor-pointer transition-all ${!formData.feltHelped ? 'border-primary bg-primary/10' : 'border-gray-200 hover:border-gray-300'}`}\r\n                    onClick={() => handleInputChange(\"feltHelped\", false)}\r\n                    aria-pressed={!formData.feltHelped}\r\n                  >\r\n                    <div className={`w-5 h-5 rounded-full border flex items-center justify-center ${!formData.feltHelped ? 'border-primary bg-primary' : 'border-gray-400'}`}>\r\n                      {!formData.feltHelped && <span className=\"text-white text-xs\">✓</span>}\r\n                    </div>\r\n                    <span>Tidak</span>\r\n                  </button>\r\n                </div>\r\n              </fieldset>\r\n            </div>\r\n\r\n            <div className=\"space-y-2\">\r\n              <div>\r\n                <label htmlFor=\"helpfulnessRating\" className=\"block text-sm font-medium mb-1\">\r\n                  Seberapa Terbantu Terang AI untuk membantu persiapan proses ujian? (1-10)\r\n                </label>\r\n                <div className=\"flex flex-col sm:flex-row items-center gap-2\">\r\n                  <div className=\"flex items-center gap-2 w-full\">\r\n                    <span className=\"text-sm font-medium\">1</span>\r\n                    <Input\r\n                      id=\"helpfulnessRating\"\r\n                      type=\"range\"\r\n                      min=\"1\"\r\n                      max=\"10\"\r\n                      value={formData.helpfulnessRating !== undefined ? formData.helpfulnessRating.toString() : '5'}\r\n                      onChange={(e) => {\r\n                        const value = e.target.value === '' ? 5 : parseInt(e.target.value);\r\n                        handleInputChange(\"helpfulnessRating\", value);\r\n                      }}\r\n                      className=\"flex-1\"\r\n                    />\r\n                    <span className=\"text-sm font-medium\">10</span>\r\n                  </div>\r\n                  <div className=\"flex items-center justify-center w-10 h-10 rounded-full bg-primary text-white font-bold\">\r\n                    {formData.helpfulnessRating || 5}\r\n                  </div>\r\n                </div>\r\n                {formErrors.helpfulnessRating && (\r\n                  <p className=\"text-danger text-sm mt-1\">{formErrors.helpfulnessRating}</p>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            <Textarea\r\n              label=\"Dari sisi mana yang merasa paling terbantu?\"\r\n              placeholder=\"Ceritakan pengalaman Anda\"\r\n              value={formData.mostHelpfulAspect}\r\n              onChange={(e) => handleInputChange(\"mostHelpfulAspect\", e.target.value)}\r\n              classNames={{\r\n                base: \"max-w-full\",\r\n                inputWrapper: \"bg-white\",\r\n                input: \"text-sm sm:text-base\"\r\n              }}\r\n              minRows={3}\r\n            />\r\n\r\n            <Textarea\r\n              label=\"Apakah ada feature yang perlu ditingkatkan agar bisa membantu proses persiapan ujian lebih maksimal?\"\r\n              placeholder=\"Berikan saran Anda\"\r\n              value={formData.improvementSuggestions}\r\n              onChange={(e) => handleInputChange(\"improvementSuggestions\", e.target.value)}\r\n              classNames={{\r\n                base: \"max-w-full\",\r\n                inputWrapper: \"bg-white\",\r\n                input: \"text-sm sm:text-base\"\r\n              }}\r\n              minRows={3}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"bg-white p-4 md:p-6 rounded-xl shadow-sm border\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"w-1 h-6 bg-primary rounded-full mr-3\"></div>\r\n            <h3 className=\"text-lg font-semibold\">Kontak Lanjutan</h3>\r\n          </div>\r\n\r\n          <div className=\"space-y-5\">\r\n            <div className=\"p-3 border border-primary/20 bg-primary/5 rounded-lg\">\r\n              <Checkbox\r\n                isSelected={formData.contactConsent}\r\n                onValueChange={(value) => handleInputChange(\"contactConsent\", value)}\r\n                classNames={{\r\n                  base: \"data-[selected=true]:border-primary\",\r\n                  label: \"text-sm md:text-base\"\r\n                }}\r\n                size=\"lg\"\r\n              >\r\n                Jika dikontak Terang AI terkait lanjutan pertanyaan lainnya, apakah bersedia?\r\n              </Checkbox>\r\n            </div>\r\n\r\n            {formData.contactConsent && (\r\n              <Input\r\n                label=\"Nomor HP/WhatsApp\"\r\n                placeholder=\"Masukkan nomor HP/WhatsApp\"\r\n                value={formData.phoneNumber}\r\n                onChange={(e) => handleInputChange(\"phoneNumber\", e.target.value)}\r\n                isInvalid={!!formErrors.phoneNumber}\r\n                errorMessage={formErrors.phoneNumber}\r\n                isRequired={formData.contactConsent}\r\n                classNames={{\r\n                  base: \"max-w-full\",\r\n                  inputWrapper: \"bg-white\"\r\n                }}\r\n              />\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex justify-center items-center w-full\">\r\n        <Button\r\n          type=\"submit\"\r\n          color=\"primary\"\r\n          isLoading={isSubmitting}\r\n          isDisabled={isSubmitting}\r\n          size=\"lg\"\r\n          className=\"px-6 sm:px-8 py-4 sm:py-6 text-sm sm:text-base font-medium whitespace-normal text-center\"\r\n          style={{ width: \"280px\", maxWidth: \"100%\" }}\r\n        >\r\n          {isSubmitting ? \"Mengirim...\" : \"Kirim Formulir\"}\r\n        </Button>\r\n      </div>\r\n    </Form>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAwBO,SAAS,oBAAoB,EAClC,WAAW,EACX,aAAa,CAAC,CAAC,EACf,UAAU,EACV,gBAAgB,EAChB,UAAU,EACV,SAAS,EACT,eAAe,KAAK,EACpB,QAAQ,EACR,SAAS,EACgB;;IACzB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC;QACzE,MAAM,YAAY;QAClB,OAAO,aAAa;QACpB,mBAAmB;QACnB,eAAe;QACf,YAAY;QACZ,mBAAmB;QACnB,wBAAwB;QACxB,gBAAgB;QAChB,aAAa;QACb,GAAI,eAAe,CAAC,CAAC;IACvB;IAEA,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,aAAa;gBACf;qDAAY,CAAC,OAAS,CAAC;4BACrB,GAAG,IAAI;4BACP,GAAG,WAAW;wBAChB,CAAC;;YACH;QACF;wCAAG;QAAC;KAAY;IAEhB,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,UAAU;gBACZ;qDAAY,CAAC,OAAS,CAAC;4BACrB,GAAG,IAAI;4BACP,MAAM;wBACR,CAAC;;YACH;YACA,IAAI,WAAW;gBACb;qDAAY,CAAC,OAAS,CAAC;4BACrB,GAAG,IAAI;4BACP,OAAO;wBACT,CAAC;;YACH;QACF;wCAAG;QAAC;QAAU;KAAU;IAExB,MAAM,oBAAoB,CAAC,OAAsC;QAC/D,0CAA0C;QAC1C,IAAI,cAAc,UAAU,CAAC,MAAM,EAAE;YACnC,WAAW;QACb;QAEA,mBAAmB;QACnB,YAAY,CAAC;YACX,MAAM,UAAU;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM;YAE1C,wCAAwC;YACxC,IAAI,kBAAkB;gBACpB,iBAAiB;YACnB;YAEA,OAAO;QACT;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,YAAY;YACd,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,WAAW;YACb,MAAM;QACR;IACF;IAEA,qBACE,6LAAC,sMAAA,CAAA,OAAI;QAAC,WAAU;QAA8B,UAAU;;0BAEtD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+MAAA,CAAA,SAAM;oBACL,MAAK;oBACL,OAAM;oBACN,SAAQ;oBACR,WAAW;oBACX,YAAY;oBACZ,MAAK;oBACL,WAAU;oBACV,OAAO;wBAAE,OAAO;wBAAS,UAAU;oBAAO;oBAC1C,SAAS;8BACV;;;;;;;;;;;0BAIH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;;;;;;;0CAGxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,iPAAA,CAAA,QAAK;wCACJ,OAAM;wCACN,aAAY;wCACZ,OAAO,SAAS,IAAI;wCACpB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;wCACzD,WAAW,CAAC,CAAC,WAAW,IAAI;wCAC5B,cAAc,WAAW,IAAI;wCAC7B,UAAU;wCACV,YAAY;4CACV,MAAM;4CACN,cAAc;wCAChB;;;;;;kDAGF,6LAAC,iPAAA,CAAA,QAAK;wCACJ,OAAM;wCACN,aAAY;wCACZ,OAAO,SAAS,KAAK;wCACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wCAC1D,WAAW,CAAC,CAAC,WAAW,KAAK;wCAC7B,cAAc,WAAW,KAAK;wCAC9B,UAAU;wCACV,YAAY;4CACV,MAAM;4CACN,cAAc;wCAChB;;;;;;;;;;;;;;;;;;kCAKN,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;;;;;;;0CAGxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iPAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,OAAM;gDACN,aAAY;gDACZ,OAAO,SAAS,eAAe,KAAK,YAAY,SAAS,eAAe,CAAC,QAAQ,KAAK;gDACtF,UAAU,CAAC;oDACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK;oDACzE,kBAAkB,mBAAmB;gDACvC;gDACA,WAAW,CAAC,CAAC,WAAW,eAAe;gDACvC,cAAc,WAAW,eAAe;gDACxC,UAAU;gDACV,YAAY;oDACV,MAAM;oDACN,cAAc;gDAChB;;;;;;0DAGF,6LAAC,iPAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,OAAM;gDACN,aAAY;gDACZ,OAAO,SAAS,qBAAqB,KAAK,YAAY,SAAS,qBAAqB,CAAC,QAAQ,KAAK;gDAClG,UAAU,CAAC;oDACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK;oDACzE,kBAAkB,yBAAyB;gDAC7C;gDACA,WAAW,CAAC,CAAC,WAAW,qBAAqB;gDAC7C,cAAc,WAAW,qBAAqB;gDAC9C,UAAU;gDACV,YAAY;oDACV,MAAM;oDACN,cAAc;gDAChB;;;;;;0DAGF,6LAAC,iPAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,OAAM;gDACN,aAAY;gDACZ,OAAO,SAAS,cAAc,KAAK,YAAY,SAAS,cAAc,CAAC,QAAQ,KAAK;gDACpF,UAAU,CAAC;oDACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK;oDACzE,kBAAkB,kBAAkB;gDACtC;gDACA,WAAW,CAAC,CAAC,WAAW,cAAc;gDACtC,cAAc,WAAW,cAAc;gDACvC,UAAU;gDACV,YAAY;oDACV,MAAM;oDACN,cAAc;gDAChB;;;;;;;;;;;;kDAIJ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAO,WAAU;;wDAA4B;sEACb,6LAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;8DAE/D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,WAAW,CAAC,4EAA4E,EAAE,SAAS,aAAa,GAAG,iCAAiC,yCAAyC;4DAC7L,SAAS,IAAM,kBAAkB,iBAAiB;4DAClD,gBAAc,SAAS,aAAa;;8EAEpC,6LAAC;oEAAI,WAAW,CAAC,6DAA6D,EAAE,SAAS,aAAa,GAAG,8BAA8B,mBAAmB;8EACvJ,SAAS,aAAa,kBAAI,6LAAC;wEAAK,WAAU;kFAAqB;;;;;;;;;;;8EAElE,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DACC,MAAK;4DACL,WAAW,CAAC,4EAA4E,EAAE,CAAC,SAAS,aAAa,GAAG,iCAAiC,yCAAyC;4DAC9L,SAAS,IAAM,kBAAkB,iBAAiB;4DAClD,gBAAc,CAAC,SAAS,aAAa;;8EAErC,6LAAC;oEAAI,WAAW,CAAC,6DAA6D,EAAE,CAAC,SAAS,aAAa,GAAG,8BAA8B,mBAAmB;8EACxJ,CAAC,SAAS,aAAa,kBAAI,6LAAC;wEAAK,WAAU;kFAAqB;;;;;;;;;;;8EAEnE,6LAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;;;;;;;0CAGxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAO,WAAU;;wDAA4B;sEACa,6LAAC;4DAAK,WAAU;sEAAc;;;;;;;;;;;;8DAEzF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,MAAK;4DACL,WAAW,CAAC,4EAA4E,EAAE,SAAS,UAAU,GAAG,iCAAiC,yCAAyC;4DAC1L,SAAS,IAAM,kBAAkB,cAAc;4DAC/C,gBAAc,SAAS,UAAU;;8EAEjC,6LAAC;oEAAI,WAAW,CAAC,6DAA6D,EAAE,SAAS,UAAU,GAAG,8BAA8B,mBAAmB;8EACpJ,SAAS,UAAU,kBAAI,6LAAC;wEAAK,WAAU;kFAAqB;;;;;;;;;;;8EAE/D,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DACC,MAAK;4DACL,WAAW,CAAC,4EAA4E,EAAE,CAAC,SAAS,UAAU,GAAG,iCAAiC,yCAAyC;4DAC3L,SAAS,IAAM,kBAAkB,cAAc;4DAC/C,gBAAc,CAAC,SAAS,UAAU;;8EAElC,6LAAC;oEAAI,WAAW,CAAC,6DAA6D,EAAE,CAAC,SAAS,UAAU,GAAG,8BAA8B,mBAAmB;8EACrJ,CAAC,SAAS,UAAU,kBAAI,6LAAC;wEAAK,WAAU;kFAAqB;;;;;;;;;;;8EAEhE,6LAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMd,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;;8DACC,6LAAC;oDAAM,SAAQ;oDAAoB,WAAU;8DAAiC;;;;;;8DAG9E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;8EACtC,6LAAC,iPAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAK;oEACL,KAAI;oEACJ,KAAI;oEACJ,OAAO,SAAS,iBAAiB,KAAK,YAAY,SAAS,iBAAiB,CAAC,QAAQ,KAAK;oEAC1F,UAAU,CAAC;wEACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK,IAAI,SAAS,EAAE,MAAM,CAAC,KAAK;wEACjE,kBAAkB,qBAAqB;oEACzC;oEACA,WAAU;;;;;;8EAEZ,6LAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;sEAExC,6LAAC;4DAAI,WAAU;sEACZ,SAAS,iBAAiB,IAAI;;;;;;;;;;;;gDAGlC,WAAW,iBAAiB,kBAC3B,6LAAC;oDAAE,WAAU;8DAA4B,WAAW,iBAAiB;;;;;;;;;;;;;;;;;kDAK3E,6LAAC,uPAAA,CAAA,WAAQ;wCACP,OAAM;wCACN,aAAY;wCACZ,OAAO,SAAS,iBAAiB;wCACjC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;wCACtE,YAAY;4CACV,MAAM;4CACN,cAAc;4CACd,OAAO;wCACT;wCACA,SAAS;;;;;;kDAGX,6LAAC,uPAAA,CAAA,WAAQ;wCACP,OAAM;wCACN,aAAY;wCACZ,OAAO,SAAS,sBAAsB;wCACtC,UAAU,CAAC,IAAM,kBAAkB,0BAA0B,EAAE,MAAM,CAAC,KAAK;wCAC3E,YAAY;4CACV,MAAM;4CACN,cAAc;4CACd,OAAO;wCACT;wCACA,SAAS;;;;;;;;;;;;;;;;;;kCAKf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;;;;;;;0CAGxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qNAAA,CAAA,WAAQ;4CACP,YAAY,SAAS,cAAc;4CACnC,eAAe,CAAC,QAAU,kBAAkB,kBAAkB;4CAC9D,YAAY;gDACV,MAAM;gDACN,OAAO;4CACT;4CACA,MAAK;sDACN;;;;;;;;;;;oCAKF,SAAS,cAAc,kBACtB,6LAAC,iPAAA,CAAA,QAAK;wCACJ,OAAM;wCACN,aAAY;wCACZ,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;wCAChE,WAAW,CAAC,CAAC,WAAW,WAAW;wCACnC,cAAc,WAAW,WAAW;wCACpC,YAAY,SAAS,cAAc;wCACnC,YAAY;4CACV,MAAM;4CACN,cAAc;wCAChB;;;;;;;;;;;;;;;;;;;;;;;;0BAOV,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+MAAA,CAAA,SAAM;oBACL,MAAK;oBACL,OAAM;oBACN,WAAW;oBACX,YAAY;oBACZ,MAAK;oBACL,WAAU;oBACV,OAAO;wBAAE,OAAO;wBAAS,UAAU;oBAAO;8BAEzC,eAAe,gBAAgB;;;;;;;;;;;;;;;;;AAK1C;GArYgB;KAAA"}}, {"offset": {"line": 835, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 841, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GAWEAN/TERANG/terang-web-ui/components/lpdp-goal-tracker/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { auth } from \"@/auth\";\r\n\r\nexport interface LpdpGoalTrackerFormData {\r\n  name: string;\r\n  email: string;\r\n  verbalReasoning: number;\r\n  quantitativeReasoning: number;\r\n  problemSolving: number;\r\n  passedLpdpTbs: boolean;\r\n  feltHelped: boolean;\r\n  helpfulnessRating: number;\r\n  mostHelpfulAspect: string;\r\n  improvementSuggestions: string;\r\n  contactConsent: boolean;\r\n  phoneNumber: string;\r\n}\r\n\r\nexport interface FormErrors {\r\n  [key: string]: string;\r\n}\r\n\r\nexport async function validateLpdpGoalTrackerForm(\r\n  formData: LpdpGoalTrackerFormData\r\n): Promise<FormErrors> {\r\n  const errors: FormErrors = {};\r\n\r\n  // Validate required fields\r\n  if (!formData.name) errors.name = \"Nama harus diisi\";\r\n  if (!formData.email) errors.email = \"Email harus diisi\";\r\n\r\n  // Validate numeric fields - make sure they are defined and valid\r\n  if (formData.verbalReasoning === undefined || formData.verbalReasoning === null)\r\n    errors.verbalReasoning = \"<PERSON>lai <PERSON>alaran Verbal harus diisi\";\r\n  else if (formData.verbalReasoning < 0)\r\n    errors.verbalReasoning = \"Nilai Penalaran Verbal tidak valid\";\r\n\r\n  if (formData.quantitativeReasoning === undefined || formData.quantitativeReasoning === null)\r\n    errors.quantitativeReasoning = \"Nilai Penalaran Kuantitatif harus diisi\";\r\n  else if (formData.quantitativeReasoning < 0)\r\n    errors.quantitativeReasoning = \"Nilai Penalaran Kuantitatif tidak valid\";\r\n\r\n  if (formData.problemSolving === undefined || formData.problemSolving === null)\r\n    errors.problemSolving = \"Nilai Pemecahan Masalah harus diisi\";\r\n  else if (formData.problemSolving < 0)\r\n    errors.problemSolving = \"Nilai Pemecahan Masalah tidak valid\";\r\n\r\n  // Validate helpfulness rating\r\n  if (formData.helpfulnessRating === undefined || formData.helpfulnessRating === null)\r\n    errors.helpfulnessRating = \"Rating harus diisi\";\r\n  else if (formData.helpfulnessRating < 1 || formData.helpfulnessRating > 10)\r\n    errors.helpfulnessRating = \"Rating harus antara 1-10\";\r\n\r\n  // Validate phone number if contact consent is given\r\n  if (formData.contactConsent && !formData.phoneNumber)\r\n    errors.phoneNumber = \"Nomor telepon harus diisi jika bersedia dihubungi\";\r\n\r\n  return errors;\r\n}\r\n\r\nexport async function submitLpdpGoalTrackerForm(\r\n  formData: LpdpGoalTrackerFormData\r\n): Promise<{ success: boolean; message: string }> {\r\n  try {\r\n    // Try to get the session, but don't require it\r\n    const session = await auth();\r\n\r\n    // Use email from session if available, otherwise use the one from the form\r\n    const userEmail = session?.user?.email || formData.email;\r\n\r\n    // If no email is available at all, that's an error\r\n    if (!userEmail) {\r\n      throw new Error(\"Email is required to submit the form\");\r\n    }\r\n\r\n    // Convert camelCase to snake_case for backend\r\n    const dataToSubmit = {\r\n      name: formData.name,\r\n      email: userEmail,\r\n      verbal_reasoning: formData.verbalReasoning,\r\n      quantitative_reasoning: formData.quantitativeReasoning,\r\n      problem_solving: formData.problemSolving,\r\n      passed_lpdp_tbs: formData.passedLpdpTbs,\r\n      felt_helped: formData.feltHelped,\r\n      helpfulness_rating: formData.helpfulnessRating,\r\n      most_helpful_aspect: formData.mostHelpfulAspect,\r\n      improvement_suggestions: formData.improvementSuggestions,\r\n      contact_consent: formData.contactConsent,\r\n      phone_number: formData.phoneNumber,\r\n    };\r\n\r\n    console.log(\"[LPDP Goal Tracker] Submitting data:\", dataToSubmit);\r\n\r\n    if (!process.env.BACKEND_BASE_URL) {\r\n      throw new Error(\"BACKEND_BASE_URL is not defined\");\r\n    }\r\n\r\n    if (!process.env.BACKEND_API_KEY) {\r\n      throw new Error(\"BACKEND_API_KEY is not defined\");\r\n    }\r\n\r\n    const response = await fetch(\r\n      `${process.env.BACKEND_BASE_URL}/v0/user-goal-tracker`,\r\n      {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          \"X-Api-Key\": process.env.BACKEND_API_KEY as string,\r\n        },\r\n        body: JSON.stringify(dataToSubmit),\r\n      }\r\n    );\r\n\r\n    const result = await response.json();\r\n\r\n    if (!response.ok) {\r\n      return {\r\n        success: false,\r\n        message: result.message || \"Failed to submit form\",\r\n      };\r\n    }\r\n\r\n    return {\r\n      success: true,\r\n      message: \"Form submitted successfully\",\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error submitting form:\", error);\r\n    return {\r\n      success: false,\r\n      message: error instanceof Error ? error.message : \"An unknown error occurred\",\r\n    };\r\n  }\r\n}\r\n\r\nexport async function checkLpdpGoalTrackerSubmission(): Promise<boolean> {\r\n  try {\r\n    const session = await auth();\r\n\r\n    // If user is not authenticated, we can't check submission status\r\n    // But we'll return false instead of throwing an error\r\n    if (!session || !session.user?.email) {\r\n      console.log(\"[LPDP Goal Tracker] User not authenticated, assuming no previous submission\");\r\n      return false;\r\n    }\r\n\r\n    const email = session.user.email;\r\n\r\n    if (!process.env.BACKEND_BASE_URL) {\r\n      throw new Error(\"BACKEND_BASE_URL is not defined\");\r\n    }\r\n\r\n    if (!process.env.BACKEND_API_KEY) {\r\n      throw new Error(\"BACKEND_API_KEY is not defined\");\r\n    }\r\n\r\n    const response = await fetch(\r\n      `${process.env.BACKEND_BASE_URL}/v0/user-goal-tracker/check-submission?email=${encodeURIComponent(email)}`,\r\n      {\r\n        method: \"GET\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          \"X-Api-Key\": process.env.BACKEND_API_KEY as string,\r\n        },\r\n      }\r\n    );\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Failed to check submission status\");\r\n    }\r\n\r\n    const result = await response.json();\r\n    return result.submitted || false;\r\n  } catch (error) {\r\n    console.error(\"Error checking submission:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;;;IAuBsB;IAsCA;IA2EA"}}, {"offset": {"line": 851, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 857, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GAWEAN/TERANG/terang-web-ui/app/lpdp-survey/actions.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { auth } from \"@/auth\";\r\n\r\n/**\r\n * Fetches the current user's data (name and email) from the database\r\n * @returns Promise containing the user's name and email or null if not found\r\n */\r\nexport async function getUserData(): Promise<{ name: string; email: string } | null> {\r\n  try {\r\n    // Get the current user's session\r\n    const session = await auth();\r\n\r\n    // If user is not authenticated, return null without error\r\n    // This makes authentication optional\r\n    if (!session || !session.user?.email) {\r\n      console.log(\"[LPDP Survey] User not authenticated, continuing as guest\");\r\n      return null;\r\n    }\r\n\r\n    const userEmail = session.user.email;\r\n\r\n    // Fetch user data from the backend API\r\n    if (!process.env.BACKEND_BASE_URL) {\r\n      throw new Error(\"BACKEND_BASE_URL is not defined\");\r\n    }\r\n\r\n    if (!process.env.BACKEND_API_KEY) {\r\n      throw new Error(\"BACKEND_API_KEY is not defined\");\r\n    }\r\n\r\n    const response = await fetch(\r\n      `${process.env.BACKEND_BASE_URL}/v1/users/emails/${userEmail}`,\r\n      {\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          \"X-Api-Key\": process.env.BACKEND_API_KEY as string,\r\n        },\r\n        cache: \"no-store\", // Don't cache the response\r\n      }\r\n    );\r\n\r\n    if (!response.ok) {\r\n      // If we can't fetch user data, just return null instead of throwing an error\r\n      // This allows the form to work without authentication\r\n      console.log(\"[LPDP Survey] Could not fetch user data, continuing as guest\");\r\n      return null;\r\n    }\r\n\r\n    const result = await response.json();\r\n\r\n    if (!result.data) {\r\n      console.log(\"[LPDP Survey] User data not found, continuing as guest\");\r\n      return null;\r\n    }\r\n\r\n    // Extract name and email from the response\r\n    const userData = {\r\n      name: `${result.data.first_name || ''} ${result.data.last_name || ''}`.trim(),\r\n      email: result.data.email\r\n    };\r\n\r\n    console.log(\"[LPDP Survey] User data fetched successfully:\", userData);\r\n\r\n    return userData;\r\n  } catch (error) {\r\n    console.error(\"[LPDP Survey] Error fetching user data:\", error);\r\n    throw error;\r\n  }\r\n}"], "names": [], "mappings": ";;;;;IAQsB"}}, {"offset": {"line": 863, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 869, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GAWEAN/TERANG/terang-web-ui/app/lpdp-survey/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON>,\r\n  CardHeader,\r\n  CardBody\r\n} from \"@heroui/react\";\r\nimport { toast, ToastContainer } from \"react-toastify\";\r\nimport \"react-toastify/dist/ReactToastify.css\";\r\n\r\nimport { LpdpGoalTrackerForm } from \"@/components/lpdp-goal-tracker/form\";\r\nimport {\r\n  LpdpGoalTrackerFormData,\r\n  FormErrors,\r\n  validateLpdpGoalTrackerForm,\r\n  submitLpdpGoalTrackerForm\r\n} from \"@/components/lpdp-goal-tracker/actions\";\r\nimport { getUserData } from \"./actions\";\r\n\r\nexport default function LpdpSurveyPage() {\r\n\r\n  // State variables\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [showSuccessScreen, setShowSuccessScreen] = useState(false);\r\n  const [formData, setFormData] = useState<Partial<LpdpGoalTrackerFormData>>({});\r\n  const [formErrors, setFormErrors] = useState<FormErrors>({});\r\n  const [userData, setUserData] = useState<{ name: string; email: string } | null>(null);\r\n\r\n  // Fetch user data when component mounts\r\n  useEffect(() => {\r\n    const fetchUserData = async () => {\r\n      try {\r\n        setIsLoading(true);\r\n        const data = await getUserData();\r\n        if (data) {\r\n          setUserData(data);\r\n        } else {\r\n          // User is not authenticated or data couldn't be fetched\r\n          // We'll continue with an empty form\r\n          console.log(\"[LPDP Survey] No user data available, continuing with empty form\");\r\n        }\r\n      } catch (error) {\r\n        console.error(\"[LPDP Survey] Error fetching user data:\", error);\r\n        // Don't show error toast since authentication is optional\r\n        console.log(\"[LPDP Survey] Continuing with empty form\");\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchUserData();\r\n  }, []);\r\n\r\n  // Helper function to show notifications\r\n  const showNotification = (message: string, type: \"success\" | \"error\" | \"info\" = \"info\") => {\r\n    toast[type](message, {\r\n      position: \"top-center\",\r\n      autoClose: 5000,\r\n      hideProgressBar: false,\r\n      closeOnClick: true,\r\n      pauseOnHover: true,\r\n      draggable: true,\r\n    });\r\n  };\r\n\r\n  // Handle form data changes\r\n  const handleFormDataChange = (data: Partial<LpdpGoalTrackerFormData>) => {\r\n    setFormData(data);\r\n  };\r\n\r\n  // Clear form errors\r\n  const handleClearError = (field: keyof LpdpGoalTrackerFormData) => {\r\n    setFormErrors((prev) => {\r\n      const newErrors = { ...prev };\r\n      delete newErrors[field];\r\n      return newErrors;\r\n    });\r\n  };\r\n\r\n  // Validate form\r\n  const validateForm = async (data: LpdpGoalTrackerFormData) => {\r\n    return await validateLpdpGoalTrackerForm(data);\r\n  };\r\n\r\n  // Handle form decline (user didn't participate in LPDP)\r\n  const handleFormDecline = async () => {\r\n    try {\r\n      setIsSubmitting(true);\r\n\r\n      // Create minimal form data with just name and email\r\n      const minimalFormData: LpdpGoalTrackerFormData = {\r\n        name: userData?.name || formData.name || \"\",\r\n        email: userData?.email || formData.email || \"\",\r\n        verbalReasoning: 1,\r\n        quantitativeReasoning: 1,\r\n        problemSolving: 1,\r\n        passedLpdpTbs: false,\r\n        feltHelped: false,\r\n        helpfulnessRating: 1, // Harus minimal 1 sesuai validasi backend (min=1,max=10)\r\n        mostHelpfulAspect: \"Tidak mengikuti LPDP atau latihan yang berkaitan\",\r\n        improvementSuggestions: \"-\",\r\n        contactConsent: false,\r\n        phoneNumber: \"-\",\r\n      };\r\n\r\n      console.log(\"[LPDP Survey] Submitting minimal data (user declined):\", minimalFormData);\r\n      const result = await submitLpdpGoalTrackerForm(minimalFormData);\r\n\r\n      if (result.success) {\r\n        // Show success screen\r\n        setShowSuccessScreen(true);\r\n\r\n        // Log successful submission\r\n        console.log(\"[LPDP Survey] Form declined and submitted successfully\");\r\n\r\n        // Show success notification\r\n        showNotification(\"Terima kasih atas respon Anda\", \"success\");\r\n\r\n        // No redirect needed, just show success screen\r\n        setTimeout(() => {\r\n          // Close the tab or stay on success screen\r\n          // We don't redirect to dashboard since user might not be authenticated\r\n        }, 3000);\r\n      } else {\r\n        // Log submission failure\r\n        console.error(\"[LPDP Survey] Form decline submission failed:\", result.message);\r\n\r\n        // Show error notification\r\n        showNotification(result.message || \"Gagal menyimpan data\", \"error\");\r\n      }\r\n    } catch (error) {\r\n      // Log submission error\r\n      console.error(\"[LPDP Survey] Error submitting declined form:\", error);\r\n\r\n      // Show error notification\r\n      const errorMessage = error instanceof Error\r\n        ? error.message\r\n        : \"Terjadi kesalahan tidak diketahui\";\r\n      showNotification(errorMessage, \"error\");\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Handle form submission\r\n  const handleFormSubmit = async () => {\r\n    if (!formData || Object.keys(formData).length === 0) {\r\n      showNotification(\"Silakan isi formulir terlebih dahulu\", \"error\");\r\n      setFormErrors({ form: \"Please fill out the form\" });\r\n      return;\r\n    }\r\n\r\n    // Ensure all numeric fields are actually numbers\r\n    const processedFormData = {\r\n      ...formData,\r\n      verbalReasoning: formData.verbalReasoning !== undefined ? Number(formData.verbalReasoning) : undefined,\r\n      quantitativeReasoning: formData.quantitativeReasoning !== undefined ? Number(formData.quantitativeReasoning) : undefined,\r\n      problemSolving: formData.problemSolving !== undefined ? Number(formData.problemSolving) : undefined,\r\n      helpfulnessRating: formData.helpfulnessRating !== undefined ? Number(formData.helpfulnessRating) : undefined,\r\n    };\r\n\r\n    console.log(\"[LPDP Survey] Form data before validation:\", processedFormData);\r\n\r\n    // Check if all required fields are present\r\n    const requiredFields: (keyof LpdpGoalTrackerFormData)[] = [\r\n      'name', 'email', 'verbalReasoning', 'quantitativeReasoning',\r\n      'problemSolving', 'helpfulnessRating'\r\n    ];\r\n\r\n    const missingFields = requiredFields.filter(field => {\r\n      const value = processedFormData[field];\r\n      return value === undefined || value === null || value === '';\r\n    });\r\n\r\n    if (missingFields.length > 0) {\r\n      const errorMessage = `Mohon lengkapi field berikut: ${missingFields.join(', ')}`;\r\n      showNotification(errorMessage, \"error\");\r\n\r\n      // Set errors for each missing field\r\n      const errors: FormErrors = {};\r\n      missingFields.forEach(field => {\r\n        errors[field] = `${field} harus diisi`;\r\n      });\r\n\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    // Cast to complete form data since we've verified required fields\r\n    const completeFormData = processedFormData as LpdpGoalTrackerFormData;\r\n\r\n    // Validate the form\r\n    const errors = await validateForm(completeFormData);\r\n    if (Object.keys(errors).length > 0) {\r\n      console.log(\"[LPDP Survey] Validation errors:\", errors);\r\n      const errorMessages = Object.values(errors).join(\", \");\r\n      showNotification(`Kesalahan validasi: ${errorMessages}`, \"error\");\r\n      setFormErrors(errors);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsSubmitting(true);\r\n\r\n      console.log(\"[LPDP Survey] Submitting form data:\", completeFormData);\r\n      const result = await submitLpdpGoalTrackerForm(completeFormData);\r\n\r\n      if (result.success) {\r\n        // Show success screen\r\n        setShowSuccessScreen(true);\r\n\r\n        // Log successful submission\r\n        console.log(\"[LPDP Survey] Form submitted successfully\");\r\n\r\n        // Show success notification\r\n        showNotification(\"Data berhasil disimpan!\", \"success\");\r\n\r\n        // No redirect needed, just show success screen\r\n        setTimeout(() => {\r\n          // Close the tab or stay on success screen\r\n          // We don't redirect to dashboard since user might not be authenticated\r\n        }, 5000);\r\n      } else {\r\n        // Log submission failure\r\n        console.error(\"[LPDP Survey] Form submission failed:\", result.message);\r\n\r\n        // Show error notification\r\n        showNotification(result.message || \"Gagal menyimpan data\", \"error\");\r\n      }\r\n    } catch (error) {\r\n      // Log submission error\r\n      console.error(\"[LPDP Survey] Error submitting form:\", error);\r\n\r\n      // Show error notification\r\n      const errorMessage = error instanceof Error\r\n        ? error.message\r\n        : \"Terjadi kesalahan tidak diketahui\";\r\n      showNotification(errorMessage, \"error\");\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // If still loading, show spinner\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"flex flex-col items-center justify-center min-h-screen p-4\">\r\n        <Spinner size=\"lg\" />\r\n        <p className=\"mt-4 text-gray-600\">Memuat data...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center justify-center min-h-screen p-4 bg-gray-50\">\r\n      <Card className=\"w-full max-w-3xl\">\r\n        <CardHeader className=\"flex items-center gap-2 sm:gap-3 bg-white p-3 sm:p-4\">\r\n          <img\r\n            src=\"https://terang.ai/_next/static/media/lpdp.b06d27d5.svg\"\r\n            alt=\"LPDP Logo\"\r\n            className=\"h-8 sm:h-10 w-auto\"\r\n          />\r\n          <div>\r\n            <h2 className=\"text-lg sm:text-xl font-medium\">Survei Hasil TBS LPDP Batch 1 2025</h2>\r\n            <p className=\"text-xs sm:text-sm text-gray-500 mt-1\">Bantu kami meningkatkan layanan untuk persiapan LPDP</p>\r\n          </div>\r\n        </CardHeader>\r\n        <CardBody className=\"p-0\">\r\n          {showSuccessScreen ? (\r\n            <div className=\"flex flex-col items-center justify-center py-8 sm:py-10 px-3 sm:px-4 text-center\">\r\n              <div className=\"w-16 h-16 sm:w-20 sm:h-20 rounded-full bg-green-100 flex items-center justify-center mb-4 sm:mb-6\">\r\n                <svg className=\"w-8 h-8 sm:w-10 sm:h-10 text-green-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                </svg>\r\n              </div>\r\n              <h3 className=\"text-xl sm:text-2xl font-semibold mb-2\">Terima Kasih!</h3>\r\n              <p className=\"text-sm sm:text-base text-gray-600 mb-4 sm:mb-6\">\r\n                Survei kamu telah berhasil dikirim. Kami sangat menghargai masukan kamu untuk meningkatkan layanan kami.\r\n              </p>\r\n              <div className=\"flex items-center justify-center gap-2 mb-4\">\r\n                <img\r\n                  src=\"https://cdn.terang.ai/images/logo/logo-terang-ai.svg\"\r\n                  alt=\"Terang AI Logo\"\r\n                  className=\"h-7 sm:h-8 w-auto\"\r\n                />\r\n              </div>\r\n              <p className=\"text-xs sm:text-sm text-gray-500 mt-6 sm:mt-8\">\r\n                Halaman akan dialihkan dalam beberapa detik...\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"flex flex-col bg-gray-50 rounded-lg\">\r\n              <LpdpGoalTrackerForm\r\n                clearError={(field: string) => handleClearError(field as keyof LpdpGoalTrackerFormData)}\r\n                formErrors={formErrors}\r\n                initialData={formData}\r\n                onFormDataChange={handleFormDataChange}\r\n                onComplete={handleFormSubmit}\r\n                onDecline={handleFormDecline}\r\n                isSubmitting={isSubmitting}\r\n                userName={userData?.name || \"\"}\r\n                userEmail={userData?.email || \"\"}\r\n              />\r\n            </div>\r\n          )}\r\n        </CardBody>\r\n      </Card>\r\n      <ToastContainer />\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAOA;AAGA;AACA;AAMA;AAhBA;AAAA;AAAA;AAAA;;;AAHA;;;;;;;;AAqBe,SAAS;;IAEtB,kBAAkB;IAClB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC,CAAC;IAC5E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAC1D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0C;IAEjF,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;0DAAgB;oBACpB,IAAI;wBACF,aAAa;wBACb,MAAM,OAAO,MAAM,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;wBAC7B,IAAI,MAAM;4BACR,YAAY;wBACd,OAAO;4BACL,wDAAwD;4BACxD,oCAAoC;4BACpC,QAAQ,GAAG,CAAC;wBACd;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2CAA2C;wBACzD,0DAA0D;wBAC1D,QAAQ,GAAG,CAAC;oBACd,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;mCAAG,EAAE;IAEL,wCAAwC;IACxC,MAAM,mBAAmB,CAAC,SAAiB,OAAqC,MAAM;QACpF,yKAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS;YACnB,UAAU;YACV,WAAW;YACX,iBAAiB;YACjB,cAAc;YACd,cAAc;YACd,WAAW;QACb;IACF;IAEA,2BAA2B;IAC3B,MAAM,uBAAuB,CAAC;QAC5B,YAAY;IACd;IAEA,oBAAoB;IACpB,MAAM,mBAAmB,CAAC;QACxB,cAAc,CAAC;YACb,MAAM,YAAY;gBAAE,GAAG,IAAI;YAAC;YAC5B,OAAO,SAAS,CAAC,MAAM;YACvB,OAAO;QACT;IACF;IAEA,gBAAgB;IAChB,MAAM,eAAe,OAAO;QAC1B,OAAO,MAAM,CAAA,GAAA,mJAAA,CAAA,8BAA2B,AAAD,EAAE;IAC3C;IAEA,wDAAwD;IACxD,MAAM,oBAAoB;QACxB,IAAI;YACF,gBAAgB;YAEhB,oDAAoD;YACpD,MAAM,kBAA2C;gBAC/C,MAAM,UAAU,QAAQ,SAAS,IAAI,IAAI;gBACzC,OAAO,UAAU,SAAS,SAAS,KAAK,IAAI;gBAC5C,iBAAiB;gBACjB,uBAAuB;gBACvB,gBAAgB;gBAChB,eAAe;gBACf,YAAY;gBACZ,mBAAmB;gBACnB,mBAAmB;gBACnB,wBAAwB;gBACxB,gBAAgB;gBAChB,aAAa;YACf;YAEA,QAAQ,GAAG,CAAC,0DAA0D;YACtE,MAAM,SAAS,MAAM,CAAA,GAAA,mJAAA,CAAA,4BAAyB,AAAD,EAAE;YAE/C,IAAI,OAAO,OAAO,EAAE;gBAClB,sBAAsB;gBACtB,qBAAqB;gBAErB,4BAA4B;gBAC5B,QAAQ,GAAG,CAAC;gBAEZ,4BAA4B;gBAC5B,iBAAiB,iCAAiC;gBAElD,+CAA+C;gBAC/C,WAAW;gBACT,0CAA0C;gBAC1C,uEAAuE;gBACzE,GAAG;YACL,OAAO;gBACL,yBAAyB;gBACzB,QAAQ,KAAK,CAAC,iDAAiD,OAAO,OAAO;gBAE7E,0BAA0B;gBAC1B,iBAAiB,OAAO,OAAO,IAAI,wBAAwB;YAC7D;QACF,EAAE,OAAO,OAAO;YACd,uBAAuB;YACvB,QAAQ,KAAK,CAAC,iDAAiD;YAE/D,0BAA0B;YAC1B,MAAM,eAAe,iBAAiB,QAClC,MAAM,OAAO,GACb;YACJ,iBAAiB,cAAc;QACjC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,yBAAyB;IACzB,MAAM,mBAAmB;QACvB,IAAI,CAAC,YAAY,OAAO,IAAI,CAAC,UAAU,MAAM,KAAK,GAAG;YACnD,iBAAiB,wCAAwC;YACzD,cAAc;gBAAE,MAAM;YAA2B;YACjD;QACF;QAEA,iDAAiD;QACjD,MAAM,oBAAoB;YACxB,GAAG,QAAQ;YACX,iBAAiB,SAAS,eAAe,KAAK,YAAY,OAAO,SAAS,eAAe,IAAI;YAC7F,uBAAuB,SAAS,qBAAqB,KAAK,YAAY,OAAO,SAAS,qBAAqB,IAAI;YAC/G,gBAAgB,SAAS,cAAc,KAAK,YAAY,OAAO,SAAS,cAAc,IAAI;YAC1F,mBAAmB,SAAS,iBAAiB,KAAK,YAAY,OAAO,SAAS,iBAAiB,IAAI;QACrG;QAEA,QAAQ,GAAG,CAAC,8CAA8C;QAE1D,2CAA2C;QAC3C,MAAM,iBAAoD;YACxD;YAAQ;YAAS;YAAmB;YACpC;YAAkB;SACnB;QAED,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA;YAC1C,MAAM,QAAQ,iBAAiB,CAAC,MAAM;YACtC,OAAO,UAAU,aAAa,UAAU,QAAQ,UAAU;QAC5D;QAEA,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,MAAM,eAAe,CAAC,8BAA8B,EAAE,cAAc,IAAI,CAAC,OAAO;YAChF,iBAAiB,cAAc;YAE/B,oCAAoC;YACpC,MAAM,SAAqB,CAAC;YAC5B,cAAc,OAAO,CAAC,CAAA;gBACpB,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,YAAY,CAAC;YACxC;YAEA,cAAc;YACd;QACF;QAEA,kEAAkE;QAClE,MAAM,mBAAmB;QAEzB,oBAAoB;QACpB,MAAM,SAAS,MAAM,aAAa;QAClC,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG,GAAG;YAClC,QAAQ,GAAG,CAAC,oCAAoC;YAChD,MAAM,gBAAgB,OAAO,MAAM,CAAC,QAAQ,IAAI,CAAC;YACjD,iBAAiB,CAAC,oBAAoB,EAAE,eAAe,EAAE;YACzD,cAAc;YACd;QACF;QAEA,IAAI;YACF,gBAAgB;YAEhB,QAAQ,GAAG,CAAC,uCAAuC;YACnD,MAAM,SAAS,MAAM,CAAA,GAAA,mJAAA,CAAA,4BAAyB,AAAD,EAAE;YAE/C,IAAI,OAAO,OAAO,EAAE;gBAClB,sBAAsB;gBACtB,qBAAqB;gBAErB,4BAA4B;gBAC5B,QAAQ,GAAG,CAAC;gBAEZ,4BAA4B;gBAC5B,iBAAiB,2BAA2B;gBAE5C,+CAA+C;gBAC/C,WAAW;gBACT,0CAA0C;gBAC1C,uEAAuE;gBACzE,GAAG;YACL,OAAO;gBACL,yBAAyB;gBACzB,QAAQ,KAAK,CAAC,yCAAyC,OAAO,OAAO;gBAErE,0BAA0B;gBAC1B,iBAAiB,OAAO,OAAO,IAAI,wBAAwB;YAC7D;QACF,EAAE,OAAO,OAAO;YACd,uBAAuB;YACvB,QAAQ,KAAK,CAAC,wCAAwC;YAEtD,0BAA0B;YAC1B,MAAM,eAAe,iBAAiB,QAClC,MAAM,OAAO,GACb;YACJ,iBAAiB,cAAc;QACjC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,iCAAiC;IACjC,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,kNAAA,CAAA,UAAO;oBAAC,MAAK;;;;;;8BACd,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;;;;;;;IAGxC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yMAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,sNAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC;gCACC,KAAI;gCACJ,KAAI;gCACJ,WAAU;;;;;;0CAEZ,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAC/C,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;kCAGzD,6LAAC,kNAAA,CAAA,WAAQ;wBAAC,WAAU;kCACjB,kCACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAyC,MAAK;wCAAO,QAAO;wCAAe,SAAQ;wCAAY,OAAM;kDAClH,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,6LAAC;oCAAE,WAAU;8CAAkD;;;;;;8CAG/D,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,KAAI;wCACJ,KAAI;wCACJ,WAAU;;;;;;;;;;;8CAGd,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;iDAK/D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,iJAAA,CAAA,sBAAmB;gCAClB,YAAY,CAAC,QAAkB,iBAAiB;gCAChD,YAAY;gCACZ,aAAa;gCACb,kBAAkB;gCAClB,YAAY;gCACZ,WAAW;gCACX,cAAc;gCACd,UAAU,UAAU,QAAQ;gCAC5B,WAAW,UAAU,SAAS;;;;;;;;;;;;;;;;;;;;;;0BAMxC,6LAAC,yKAAA,CAAA,iBAAc;;;;;;;;;;;AAGrB;GApSwB;KAAA"}}, {"offset": {"line": 1296, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}