package model

import (
	"time"
)

// InterviewSection represents a section within an interview
type InterviewSection struct {
	ID       string `json:"id" db:"id"`
	Name     string `json:"name" db:"name"`
	Duration string `json:"duration" db:"duration"`
}

// AvailableInterview represents an available interview in the system
type AvailableInterview struct {
	ID             string              `json:"id" db:"id"`
	Name           string              `json:"name" db:"name"`
	Subname        string              `json:"subname" db:"subname"`
	Description    string              `json:"description" db:"description"`
	BaselinePrice  float64             `json:"baseline_price" db:"baseline_price"`
	Visibility     string              `json:"visibility" db:"visibility"`
	Duration       string              `json:"duration" db:"duration"`
	Type           string              `json:"type" db:"type"`
	Subject        *string             `json:"subject" db:"subject"`
	CreatedAt      time.Time           `json:"created_at" db:"created_at"`
	ModifiedAt     time.Time           `json:"modified_at" db:"modified_at"`
	UserID         string              `json:"user_id" db:"user_id"`
	MediaURL       *string             `json:"media_url" db:"media_url"`
	CategoryName   *string             `json:"category_name" db:"category_name"`
	CategoryID     *string             `json:"category_id" db:"category_id"`
	IsPurchased    bool                `json:"is_purchased" db:"is_purchased"`
	PurchaseDate   *time.Time          `json:"purchase_date" db:"purchase_date"`
	IsFreeAccess   string              `json:"is_free_access" db:"is_free_access"`
	InterviewType  string              `json:"interview_type" db:"interview_type"`
	Sections       []InterviewSection  `json:"sections" db:"sections"`
}

// CreateAvailableInterviewRequest represents the request payload for creating an interview
type CreateAvailableInterviewRequest struct {
	Name           string              `json:"name" binding:"required"`
	Subname        string              `json:"subname" binding:"required"`
	Description    string              `json:"description" binding:"required"`
	BaselinePrice  float64             `json:"baseline_price"`
	Visibility     string              `json:"visibility"`
	Duration       string              `json:"duration" binding:"required"`
	Type           string              `json:"type"`
	Subject        *string             `json:"subject"`
	UserID         string              `json:"user_id" binding:"required"`
	MediaURL       *string             `json:"media_url"`
	CategoryName   *string             `json:"category_name"`
	CategoryID     *string             `json:"category_id"`
	IsPurchased    bool                `json:"is_purchased"`
	PurchaseDate   *time.Time          `json:"purchase_date"`
	IsFreeAccess   string              `json:"is_free_access"`
	InterviewType  string              `json:"interview_type"`
	Sections       []InterviewSection  `json:"sections"`
}

// UpdateAvailableInterviewRequest represents the request payload for updating an interview
type UpdateAvailableInterviewRequest struct {
	Name           *string             `json:"name"`
	Subname        *string             `json:"subname"`
	Description    *string             `json:"description"`
	BaselinePrice  *float64            `json:"baseline_price"`
	Visibility     *string             `json:"visibility"`
	Duration       *string             `json:"duration"`
	Type           *string             `json:"type"`
	Subject        *string             `json:"subject"`
	MediaURL       *string             `json:"media_url"`
	CategoryName   *string             `json:"category_name"`
	CategoryID     *string             `json:"category_id"`
	IsPurchased    *bool               `json:"is_purchased"`
	PurchaseDate   *time.Time          `json:"purchase_date"`
	IsFreeAccess   *string             `json:"is_free_access"`
	InterviewType  *string             `json:"interview_type"`
	Sections       *[]InterviewSection `json:"sections"`
}

// AvailableInterviewResponse represents the response structure for interview operations
type AvailableInterviewResponse struct {
	Data   interface{} `json:"data"`
	Status string      `json:"status"`
	Msg    string      `json:"msg"`
}

// AvailableInterviewListResponse represents the response structure for listing interviews
type AvailableInterviewListResponse struct {
	Data   []AvailableInterview `json:"data"`
	Status string               `json:"status"`
	Msg    string               `json:"msg"`
}
