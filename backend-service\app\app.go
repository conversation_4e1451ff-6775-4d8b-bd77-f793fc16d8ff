package app

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/redis/go-redis/v9"
	"github.com/terang-ai/backend-service/datastores/db"
	"github.com/terang-ai/backend-service/datastores/rediscache"
	"github.com/terang-ai/backend-service/middleware"
	"gopkg.in/yaml.v2"

	custom "github.com/terang-ai/backend-service/app/custom"
	exam_bundles "github.com/terang-ai/backend-service/app/exam_bundles"
	"github.com/terang-ai/backend-service/app/questions"
	"github.com/terang-ai/backend-service/app/routes/available_exams"
	"github.com/terang-ai/backend-service/app/routes/categories"
	"github.com/terang-ai/backend-service/app/routes/exams"
	"github.com/terang-ai/backend-service/app/routes/health_check"
	"github.com/terang-ai/backend-service/app/routes/invoices"
	media "github.com/terang-ai/backend-service/app/routes/media"
	"github.com/terang-ai/backend-service/app/routes/orders"
	"github.com/terang-ai/backend-service/app/routes/payments"
	"github.com/terang-ai/backend-service/app/routes/tags"
	termsOfService "github.com/terang-ai/backend-service/app/routes/terms_of_services"
	"github.com/terang-ai/backend-service/app/routes/users"
	"github.com/terang-ai/backend-service/migrations"
	"github.com/terang-ai/backend-service/notifications"
)

// MigrationLogger provides structured logging for migrations
type MigrationLogger struct {
	debug    bool
	notifier *notifications.MigrationNotifier
}

func NewMigrationLogger(debug bool) (*MigrationLogger, error) {
	// Get Mailgun configuration
	config, err := notifications.GetMailgunConfig()
	if err != nil {
		return nil, fmt.Errorf("failed to get mailgun config: %v", err)
	}

	// Create and validate notifier
	notifier := notifications.NewMigrationNotifier(
		config.Mailgun.Domain,
		config.Mailgun.APIKey,
		config.Mailgun.Sender,
		config.Mailgun.Recipients,
	)

	// Run diagnostics on the notifier
	result, err := notifier.RunDiagnostics()
	if err != nil {
		return nil, fmt.Errorf("migration notifier diagnostics failed: %v", err)
	}

	// Log diagnostic results
	log.Printf("[Migration] Config source: %s", result.ConfigSource)
	log.Printf("[Migration] Domain configured: %v", result.Domain != "")
	log.Printf("[Migration] API key configured: %v", result.HasAPIKey)
	log.Printf("[Migration] Sender configured: %v", result.HasSender)
	log.Printf("[Migration] Recipients configured: %v", result.HasRecipients)

	if result.ConnectionTest != nil {
		return nil, fmt.Errorf("mailgun connection test failed: %v", result.ConnectionTest)
	}

	return &MigrationLogger{
		debug:    debug,
		notifier: notifier,
	}, nil
}

func (ml *MigrationLogger) Debug(format string, args ...interface{}) {
	if ml.debug {
		message := fmt.Sprintf(format, args...)
		log.Printf("[Migration][DEBUG] %s", message)
		if ml.notifier != nil {
			ml.notifier.LogEvent("DEBUG", message, nil)
		}
	}
}

func (ml *MigrationLogger) Warning(format string, args ...interface{}) {
	message := fmt.Sprintf(format, args...)
	log.Printf("[Migration][WARNING] %s", message)
	if ml.notifier != nil {
		ml.notifier.LogEvent("WARNING", message, nil)
	}
}

func (ml *MigrationLogger) Info(format string, args ...interface{}) {
	message := fmt.Sprintf(format, args...)
	log.Printf("[Migration][INFO] %s", message)
	if ml.notifier != nil {
		// Enhanced detection of migration files and SQL
		if strings.HasPrefix(message, "MIGRATION_SQL_FILE:") {
			ml.notifier.LogEvent("MigrationFile", message, nil)
		} else if strings.HasPrefix(message, "MIGRATION_SQL_CONTENT:") {
			ml.notifier.LogEvent("MigrationSQL", message, nil)
		} else if strings.HasPrefix(message, "MIGRATION_SQL_STMT_") {
			ml.notifier.LogEvent("MigrationStmt", message, nil)
		} else if strings.HasPrefix(message, "MIGRATION_DDL_STMT:") {
			ml.notifier.LogEvent("MigrationDDL", message, nil)
		} else if strings.HasPrefix(message, "MIGRATION_SCHEMA_CHANGE:") {
			ml.notifier.LogEvent("SchemaChange", message, nil)
		} else if strings.Contains(format, "File:") || strings.Contains(message, "File:") {
			ml.notifier.LogEvent("Migration", message, nil)
		} else if strings.Contains(format, "SQL Statement") || strings.Contains(message, "SQL Statement") {
			ml.notifier.LogEvent("SQL", message, nil)
		} else if strings.Contains(format, "DDL Statement") || strings.Contains(message, "DDL Statement") {
			ml.notifier.LogEvent("DDL", message, nil)
		} else if strings.Contains(message, "DDL:") {
			ml.notifier.LogEvent("DDL", message, nil)
		} else if strings.Contains(message, "SQL:") {
			ml.notifier.LogEvent("SQL", message, nil)
		} else {
			ml.notifier.LogEvent("INFO", message, nil)
		}
	}
}

func (ml *MigrationLogger) Error(format string, args ...interface{}) {
	message := fmt.Sprintf(format, args...)
	log.Printf("[Migration][ERROR] %s", message)
	if ml.notifier != nil {
		ml.notifier.LogEvent("ERROR", message, nil)
	}
}

type App struct {
	DB       *sql.DB
	DBx      *sqlx.DB
	Router   *gin.Engine
	REDIS    *redis.Client
	Migrator *migrations.Migrator
	logger   *MigrationLogger
}

func (a *App) SetupMigrations(debug bool) error {
	var err error
	// Use NewMigrationLogger instead of creating logger manually
	a.logger, err = NewMigrationLogger(debug)
	if err != nil {
		return fmt.Errorf("failed to setup migration logger: %v", err)
	}

	a.logger.Info("Setting up database migrations...")

	// Verify database connection
	if err := a.DB.Ping(); err != nil {
		a.logger.Error("Database connection check failed: %v", err)
		return fmt.Errorf("database connection check failed: %v", err)
	}

	// Initialize migrator with our logger
	migrator, err := migrations.NewMigrator(a.DB, a.logger)
	if err != nil {
		a.logger.Error("Failed to setup migrator: %v", err)
		return fmt.Errorf("failed to setup migrator: %v", err)
	}

	a.Migrator = migrator

	// Now get the version after migrator is initialized
	version, dirty, _ := a.Migrator.Version()
	a.logger.notifier.SetInitialState(version, dirty)

	// Scan migration files to provide better logging
	files, err := a.Migrator.GetMigrationFiles()
	if err == nil {
		a.logger.Info("Found %d migration files", len(files))
		if debug {
			for i, file := range files {
				a.logger.Debug("Migration file %d: %s", i+1, file)
			}
		}
	}

	a.logger.Info("Migration setup completed successfully")
	return nil
}

func (a *App) RunMigrations(debug bool) error {
	start := time.Now()
	a.logger.Info("Starting database migration process...")

	// Get initial DB state
	initialVersion, initialDirty, versionErr := a.Migrator.Version()
	if versionErr == nil {
		a.logger.notifier.SetInitialState(initialVersion, initialDirty)
	}

	// Take a snapshot of the schema before migration
	if err := a.Migrator.CaptureSchemaSnapshot("before"); err != nil && debug {
		a.logger.Debug("Failed to capture initial schema state: %v", err)
	}

	// Get all migration files before execution to know what we'll be running
	migrationFiles, _ := a.Migrator.GetMigrationFiles()
	if len(migrationFiles) > 0 {
		a.logger.Info("Found %d migration files to process", len(migrationFiles))
		for _, file := range migrationFiles {
			// Only add "up" migrations to the notification list
			if isUpMigration(file) {
				a.logger.Info("Migration: File: %s", file)
			}
		}
	}

	// Run migrations and capture any error
	finalErr := a.executeMigrationSteps()

	// Take a snapshot after migration
	if err := a.Migrator.CaptureSchemaSnapshot("after"); err != nil && debug {
		a.logger.Debug("Failed to capture final schema state: %v", err)
	}

	// Get final version
	finalVersion, finalDirty, _ := a.Migrator.Version()
	tables, _ := a.Migrator.GetMigrationTables()

	// Log a clean summary
	if initialVersion != finalVersion {
		// Something changed, log the change
		a.logger.Info("Successfully migrated database from version %d to %d", initialVersion, finalVersion)

		// Get schema changes
		schemaDiff := a.Migrator.GetSchemaDiff()

		// Only log actual changes, not headers or summary info
		for _, change := range schemaDiff {
			if strings.HasPrefix(change, "➕") || strings.HasPrefix(change, "❌") {
				a.logger.Info("Schema Change: %s", change)
			}
		}
	} else {
		a.logger.Info("No migration needed - database already at version %d", initialVersion)
	}

	// Set final state and send summary
	a.logger.notifier.SetFinalState(finalVersion, finalDirty, tables, finalErr)
	if err := a.logger.notifier.SendMigrationSummary(); err != nil {
		log.Printf("Failed to send migration summary email: %v", err)
	}

	duration := time.Since(start)
	if finalErr != nil {
		a.logger.Error("Database migrations failed after %v: %v", duration, finalErr)
	} else {
		a.logger.Info("Database migrations completed successfully in %v", duration)
	}

	return finalErr
}

func (a *App) verifyDatabaseConnection() error {
	a.logger.Debug("Verifying database connection...")

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := a.DB.PingContext(ctx); err != nil {
		a.logger.Error("Database connection verification failed: %v", err)
		return fmt.Errorf("database connection verification failed: %v", err)
	}

	a.logger.Debug("Database connection verified successfully")
	return nil
}

func (a *App) checkSchemaMigrationsTable() (bool, error) {
	a.logger.Debug("Checking for schema_migrations table...")

	var exists bool
	query := `
		SELECT EXISTS (
			SELECT FROM information_schema.tables
			WHERE table_schema = 'public'
			AND table_name = 'schema_migrations'
		);
	`

	err := a.DB.QueryRow(query).Scan(&exists)
	if err != nil {
		a.logger.Error("Error checking schema_migrations table: %v", err)
		return false, fmt.Errorf("failed to check schema_migrations table: %v", err)
	}

	a.logger.Debug("Schema migrations table exists: %v", exists)
	return exists, nil
}

func (a *App) handleMissingMigrationsTable() error {
	a.logger.Info("Schema migrations table not found, performing full reset...")

	if err := a.Migrator.ResetMigrations(); err != nil {
		a.logger.Error("Failed to reset migrations: %v", err)
		return fmt.Errorf("failed to reset migrations: %v", err)
	}

	a.logger.Info("Migration reset completed successfully")
	return nil
}

func (a *App) runMigrationsWithRetry(maxRetries int) error {
	var lastError error

	for attempt := 1; attempt <= maxRetries; attempt++ {
		a.logger.Info("Running migrations (attempt %d/%d)...", attempt, maxRetries)

		// Get the current version BEFORE migration to know what migration files to analyze
		currentVersion, _, err := a.Migrator.Version()
		if err == nil {
			a.logger.Info("Current version before migration: %d", currentVersion)
		}

		// Run the migration
		if err := a.Migrator.Up(); err != nil {
			lastError = err
			a.logger.Error("Migration attempt %d failed: %v", attempt, err)

			if attempt < maxRetries {
				time.Sleep(time.Duration(attempt) * time.Second)
				continue
			}
			return fmt.Errorf("failed to run migrations after %d attempts: %v", maxRetries, lastError)
		}

		// Get the new version after successful migration
		newVersion, _, vErr := a.Migrator.Version()
		if vErr == nil && newVersion > currentVersion {
			a.logger.Info("Successfully migrated from version %d to %d", currentVersion, newVersion)

			// Now that we know which migrations were run, find the most recent migration file
			migrateDir := "migrations/files"

			// Look for any migration file matching the new version
			pattern := fmt.Sprintf("%06d_*.up.sql", newVersion)
			matches, err := filepath.Glob(filepath.Join(migrateDir, pattern))

			if err != nil {
				a.logger.Error("Failed to search for migration files: %v", err)
			} else if len(matches) > 0 {
				// Use the first matching file (there should typically be only one)
				filePath := matches[0]
				filename := filepath.Base(filePath)

				// Attempt to read the file
				content, err := os.ReadFile(filePath)
				if err != nil {
					a.logger.Error("Failed to read migration file: %v", err)
				} else {
					fileContent := string(content)
					a.logger.Info("Migration SQL Content for file %s:", filename)
					a.logger.Info("SQL: %s", fileContent)

					// Also extract individual statements
					statements := extractMigrationStatements(fileContent)
					for i, stmt := range statements {
						stmt = strings.TrimSpace(stmt)
						if stmt == "" || strings.HasPrefix(stmt, "--") || strings.HasPrefix(stmt, "/*") {
							continue
						}

						a.logger.Info("SQL Statement %d: %s", i+1, stmt)

						// Also log as DDL for schema changes
						if isSchemaChangingSQL(stmt) {
							a.logger.Info("DDL: %s", stmt)
						}
					}
				}
			} else {
				a.logger.Warning("No migration file found for version %d", newVersion)
			}
		}

		a.logger.Info("Migrations completed successfully on attempt %d", attempt)
		return nil
	}

	return lastError
}

func (a *App) processMigrationFileForNotification(filename string) error {
	// Build the path to the migration file
	migrateDir := "migrations/files" // Use same path as Migrator
	filePath := filepath.Join(migrateDir, filename)

	// Read the file content
	content, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("error reading migration file: %v", err)
	}

	// Extract SQL statements
	fileContent := string(content)

	// First, log the entire file content with clear markers for notification capture
	a.logger.Info("MIGRATION_SQL_FILE: %s", filename)
	a.logger.Info("MIGRATION_SQL_CONTENT: %s", fileContent)

	// Extract individual statements
	statements := extractMigrationStatements(fileContent)

	// Log each statement with a specific format for notification capture
	for i, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" || strings.HasPrefix(stmt, "--") || strings.HasPrefix(stmt, "/*") {
			continue // Skip comments and empty lines
		}

		// Log the raw SQL statement with a clear prefix for notification capture
		a.logger.Info("MIGRATION_SQL_STMT_%d: %s", i+1, stmt)

		// Log DDL statements specially to ensure they appear in notifications
		if isSchemaChangingSQL(stmt) {
			// Use specific format for DDL statements
			a.logger.Info("MIGRATION_DDL_STMT: %s", stmt)

			// Also log a human-readable description
			schemaDesc := generateSchemaChangeDescription(stmt)
			if schemaDesc != "" {
				a.logger.Info("MIGRATION_SCHEMA_CHANGE: %s", schemaDesc)
			}
		}
	}

	return nil
}

func filterMigrationsByVersion(files []string, fromVersion, toVersion uint) []string {
	var relevant []string

	for _, file := range files {
		version := extractVersionFromFilename(file)
		if version > fromVersion && version <= toVersion {
			relevant = append(relevant, file)
		}
	}

	return relevant
}

// extractVersionFromFilename extracts the version number from a migration filename
// For example, from "000022_add_column.up.sql" it would extract 22
func extractVersionFromFilename(filename string) uint {
	parts := strings.Split(filename, "_")
	if len(parts) > 0 {
		versionStr := parts[0]
		version, err := strconv.ParseUint(versionStr, 10, 32)
		if err == nil {
			return uint(version)
		}
	}
	return 0
}

// isUpMigration checks if a migration file is an "up" migration (as opposed to a "down" migration)
func isUpMigration(filename string) bool {
	return strings.HasSuffix(filename, ".up.sql")
}

// Extract SQL statements from a migration file
func extractMigrationStatements(content string) []string {
	// Simple statement extraction by splitting on semicolons
	// This is a simplified version - a proper parser would be more robust
	var statements []string
	var currentStmt string
	inQuote := false
	inLineComment := false
	inBlockComment := false

	for i := 0; i < len(content); i++ {
		char := content[i]

		// Handle line comments
		if !inQuote && !inBlockComment && i < len(content)-1 && char == '-' && content[i+1] == '-' {
			inLineComment = true
		}

		// End of line comment
		if inLineComment && char == '\n' {
			inLineComment = false
		}

		// Handle block comments
		if !inQuote && !inLineComment && i < len(content)-1 && char == '/' && content[i+1] == '*' {
			inBlockComment = true
		}

		// End of block comment
		if inBlockComment && i > 0 && content[i-1] == '*' && char == '/' {
			inBlockComment = false
		}

		// Handle quotes (simple, not accounting for escaped quotes)
		if !inLineComment && !inBlockComment && char == '\'' {
			inQuote = !inQuote
		}

		// Add the character to the current statement
		currentStmt += string(char)

		// If we hit a semicolon and we're not in a quote, comment, etc., end the statement
		if char == ';' && !inQuote && !inLineComment && !inBlockComment {
			statements = append(statements, currentStmt)
			currentStmt = ""
		}
	}

	// Add the last statement if it doesn't end with a semicolon
	if strings.TrimSpace(currentStmt) != "" {
		statements = append(statements, currentStmt)
	}

	return statements
}

// Check if SQL is a schema-changing statement
func isSchemaChangingSQL(sql string) bool {
	sqlUpper := strings.ToUpper(sql)
	return strings.Contains(sqlUpper, "CREATE TABLE") ||
		strings.Contains(sqlUpper, "ALTER TABLE") ||
		strings.Contains(sqlUpper, "DROP TABLE") ||
		strings.Contains(sqlUpper, "CREATE INDEX") ||
		strings.Contains(sqlUpper, "DROP INDEX") ||
		strings.Contains(sqlUpper, "CREATE TYPE") ||
		strings.Contains(sqlUpper, "ALTER TYPE") ||
		strings.Contains(sqlUpper, "DROP TYPE")
}

// Generate a readable description of a schema change
func generateSchemaChangeDescription(sql string) string {
	sqlUpper := strings.ToUpper(sql)

	// Extract table/object name using regex
	var description string

	if strings.Contains(sqlUpper, "CREATE TABLE") {
		tableName := extractObjectNameFromSQL(sql, "CREATE TABLE")
		if tableName != "" {
			description = fmt.Sprintf("Creating table '%s'", tableName)
		}
	} else if strings.Contains(sqlUpper, "ALTER TABLE") {
		tableName := extractObjectNameFromSQL(sql, "ALTER TABLE")

		if strings.Contains(sqlUpper, "ADD COLUMN") {
			columnName := extractObjectNameFromSQL(sql, "ADD COLUMN")
			if tableName != "" && columnName != "" {
				description = fmt.Sprintf("Adding column '%s' to table '%s'", columnName, tableName)
			}
		} else if strings.Contains(sqlUpper, "DROP COLUMN") {
			columnName := extractObjectNameFromSQL(sql, "DROP COLUMN")
			if tableName != "" && columnName != "" {
				description = fmt.Sprintf("Dropping column '%s' from table '%s'", columnName, tableName)
			}
		} else if strings.Contains(sqlUpper, "ALTER COLUMN") {
			columnName := extractObjectNameFromSQL(sql, "ALTER COLUMN")
			if tableName != "" && columnName != "" {
				description = fmt.Sprintf("Altering column '%s' on table '%s'", columnName, tableName)
			}
		} else if tableName != "" {
			description = fmt.Sprintf("Altering table '%s'", tableName)
		}
	} else if strings.Contains(sqlUpper, "DROP TABLE") {
		tableName := extractObjectNameFromSQL(sql, "DROP TABLE")
		if tableName != "" {
			description = fmt.Sprintf("Dropping table '%s'", tableName)
		}
	} else if strings.Contains(sqlUpper, "CREATE INDEX") {
		indexName := extractObjectNameFromSQL(sql, "CREATE INDEX")
		if indexName != "" {
			description = fmt.Sprintf("Creating index '%s'", indexName)

			// Try to extract table name from "ON" clause
			reTable := regexp.MustCompile(`(?i)ON\s+([^\s(]+)`)
			matches := reTable.FindStringSubmatch(sql)
			if len(matches) >= 2 {
				tableName := strings.Trim(matches[1], `"'`)
				description += fmt.Sprintf(" on table '%s'", tableName)
			}
		}
	} else if strings.Contains(sqlUpper, "DROP INDEX") {
		indexName := extractObjectNameFromSQL(sql, "DROP INDEX")
		if indexName != "" {
			description = fmt.Sprintf("Dropping index '%s'", indexName)
		}
	} else if strings.Contains(sqlUpper, "CREATE TYPE") {
		typeName := extractObjectNameFromSQL(sql, "CREATE TYPE")
		if typeName != "" {
			description = fmt.Sprintf("Creating type '%s'", typeName)
		}
	} else if strings.Contains(sqlUpper, "ALTER TYPE") {
		typeName := extractObjectNameFromSQL(sql, "ALTER TYPE")
		if typeName != "" {
			description = fmt.Sprintf("Altering type '%s'", typeName)
		}
	} else if strings.Contains(sqlUpper, "DROP TYPE") {
		typeName := extractObjectNameFromSQL(sql, "DROP TYPE")
		if typeName != "" {
			description = fmt.Sprintf("Dropping type '%s'", typeName)
		}
	}

	return description
}

// Extract object name from SQL using regex
func extractObjectNameFromSQL(sql, keyword string) string {
	// First make the regex pattern case insensitive
	pattern := fmt.Sprintf(`(?i)%s\s+(?:IF\s+(?:NOT\s+)?EXISTS\s+)?([^\s(;]+)`, regexp.QuoteMeta(keyword))
	re := regexp.MustCompile(pattern)
	matches := re.FindStringSubmatch(sql)

	if len(matches) >= 2 {
		// Clean up quotes and return
		return strings.Trim(matches[1], `"'`)
	}

	return ""
}

// Update executeMigrationSteps to use our improved runMigrationsWithRetry
func (a *App) executeMigrationSteps() error {
	// 1. Check database connection health
	if err := a.verifyDatabaseConnection(); err != nil {
		return fmt.Errorf("database connection verification failed: %v", err)
	}

	// Log that we're starting migration process for better visibility
	a.logger.Info("Beginning migration execution process...")

	// 2. Check and handle schema_migrations table
	exists, err := a.checkSchemaMigrationsTable()
	if err != nil {
		return fmt.Errorf("schema migrations table check failed: %v", err)
	}

	if !exists {
		if err := a.handleMissingMigrationsTable(); err != nil {
			return fmt.Errorf("failed to handle missing migrations table: %v", err)
		}
	}

	// Get current version before migrations
	currentVersion, _, err := a.Migrator.Version()
	if err == nil {
		a.logger.Info("Current version before migration: %d", currentVersion)
	}

	// 3. Run migrations with our improved retry mechanism
	if err := a.runMigrationsWithRetry(3); err != nil {
		return fmt.Errorf("migration retry process failed: %v", err)
	}

	// Get new version after migrations
	newVersion, _, err := a.Migrator.Version()
	if err == nil && newVersion > currentVersion {
		a.logger.Info("Successfully migrated from version %d to %d", currentVersion, newVersion)

		// Get all migration files that were executed
		migrationFiles, err := a.Migrator.GetMigrationFiles()
		if err == nil {
			// Filter for files between current and new version
			relevantMigrations := filterMigrationsByVersion(migrationFiles, currentVersion, newVersion)

			if len(relevantMigrations) > 0 {
				a.logger.Info("Processing %d relevant migration files for notifications", len(relevantMigrations))

				for _, file := range relevantMigrations {
					// Only process "up" migrations
					if isUpMigration(file) {
						// Log the migration file clearly
						a.logger.Info("Migration File: %s", file)

						// Extract and process the SQL from each migration file
						err := a.processMigrationFileForNotification(file)
						if err != nil {
							a.logger.Error("Error processing migration file %s: %v", file, err)
						}
					}
				}
			}
		}
	}

	// 4. Perform post-migration checks
	if err := a.performPostMigrationChecks(); err != nil {
		return fmt.Errorf("post-migration checks failed: %v", err)
	}

	// Log completion for better visibility
	a.logger.Info("Migration execution steps completed successfully")

	return nil
}

func (a *App) performPostMigrationChecks() error {
	a.logger.Debug("Performing post-migration checks...")

	// Check migration version
	version, dirty, err := a.Migrator.Version()
	if err != nil {
		a.logger.Error("Failed to get migration version: %v", err)
		return fmt.Errorf("failed to get migration version: %v", err)
	}

	if dirty {
		a.logger.Error("Database is in dirty state at version %d", version)
		return fmt.Errorf("database is in dirty state at version %d", version)
	}

	// Check for required tables - but only log this in debug mode
	allTablesExist, missingTables := a.Migrator.CheckTables()
	if !allTablesExist {
		a.logger.Error("Some tables are missing after migration: %v", missingTables)
		return fmt.Errorf("migration validation failed: missing tables: %v", missingTables)
	}

	a.logger.Debug("Post-migration checks completed successfully. Current version: %d", version)
	return nil
}

func (a *App) CreateConnection() {
	log.Println("Setting up datastore connection...")

	// Creating DB connection
	log.Println("Creating DB connection...")
	dbp, err := db.Connectdb()
	if err != nil {
		log.Fatalf("Failed to connect to PostgreSQL: %v", err)
	}
	a.DB = dbp
	log.Println("PostgreSQL connection established successfully.")
	dbx, err := db.Connectdbx()
	if err != nil {
		log.Fatalf("Failed to connect to PostgreSQL: %v", err)
	}
	a.DBx = dbx
	log.Println("PostgreSQLX (sqlX) connection established successfully.")

	// Creating Redis connection
	log.Println("Creating Redis connection...")
	redisClient, err := rediscache.ConnectRedis(context.Background())
	if err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	a.REDIS = redisClient
	log.Println("Redis connection established successfully.")
}

func (a *App) Routes() {
	gin.SetMode(gin.ReleaseMode)

	r := gin.New() // Use New() instead of Default() to avoid default loggers

	// Add our custom logger
	r.Use(middleware.CustomLogger())
	r.Use(gin.Recovery()) // Keep the recovery middleware

	// Example of more customized CORS configuration
	corsConfig := cors.Config{
		AllowOrigins:     []string{"http://localhost:80", "http://localhost:8000", "https://terang.ai", "https://dev.terang.ai"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE"},
		AllowHeaders:     []string{"Content-Type", "Authorization", "x-api-key"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}
	r.Use(cors.New(corsConfig))

	// Register health check routes
	health_check.RegisterRoutes(r, a.DB, a.REDIS)

	// Use API Key (only below this function will use the api key header below)
	r.Use(middleware.APIKeyAuth())

	// User API registration
	users.RegisterRoutes(r, a.DB, a.REDIS)

	// User API registration
	users.RegisterUserAnalyticsRoutes(r, a.DBx)

	// Available Exams API registration
	available_exams.RegisterRoutes(r, a.DBx, a.REDIS)
	available_exams.RegisterDaerah3TRoutes(r, a.DBx, a.REDIS)
	custom.RegisterDaerah3TRoutes(r, a.DBx, a.REDIS)

	// Exam Sessions and scores API registration
	exams.RegisterRoutes(r, a.DBx, a.REDIS)

	// Category API registration
	categories.RegisterRoutes(r, a.DB, a.REDIS)

	// Tag API registration
	tags.RegisterRoutes(r, a.DB, a.REDIS)

	// TermsOfService API registration
	termsOfService.RegisterRoutes(r, a.DB, a.REDIS)

	// Order API registration
	orders.RegisterRoutes(r, a.DB, a.REDIS)

	// Order API registration
	payments.RegisterRoutes(r, a.DB, a.REDIS)
	custom.RegisterPaymentReconciliationRoutes(r, a.DBx)

	// Invoices API registration
	invoices.RegisterRoutes(r, a.DB, a.REDIS)

	// Wallets API registration
	media.RegisterRoutes(r, a.DBx, a.REDIS)

	// custom generic (need to refactor later)
	custom.RegisterRoutes(r, a.DBx, a.REDIS)
	custom.RegisterExamConfigRoute(r, a.DBx, a.REDIS)
	custom.RegisterPracticeCompletionsRoute(r, a.DBx)
	custom.RegisterExamGradingRoutes(r, a.DBx)
	custom.RegisterInterviewSessionsRoutes(r, a.DBx)

	// gamification routes
	custom.RegisterGamificationRoutes(r, a.DBx)

	// subscruption routes
	custom.RegisterSubscriptionRoutes(r, a.DBx)
	custom.RegisterTimeManagementRoutes(r, a.DBx, a.REDIS)
	// custom.StartSubscriptionMaintenanceCron(r, a.DBx)

	// chat ai history routes
	custom.RegisterChatAIHistoryRoutes(r, a.DBx)

	// ai token routes
	custom.RegisterAITokenRoutes(r, a.DBx)

	// ai interview grading routes
	apiKey, err := middleware.GetGeminiAPIKey()
	if err != nil {
		log.Printf("⚠️ [WARNING] Failed to load Gemini API key: %v", err)
		apiKey = "" // Will fallback to environment in the handler
	}

	aiConfig := &custom.AIConfig{
		GoogleGeminiAPIKey: apiKey,
	}

	// Add debug logging
	if apiKey == "" {
		log.Printf("⚠️ [WARNING] No Gemini API key found in config or environment")
	} else {
		log.Printf("✅ [SUCCESS] Gemini API key loaded (length: %d)", len(apiKey))
	}

	custom.RegisterAIInterviewGradingRoutes(r, a.DBx, a.REDIS, aiConfig)

	// referral program
	custom.RegisterReferralRoutes(r, a.DBx)

	// promo routes
	custom.RegisterPromoRoutes(r, a.DBx)

	// question insertion for RAG
	questions.RegisterRoutes(r, a.DBx)

	// find universities
	uniIndex := custom.NewUniversityIndex()
	custom.RegisterUniversityRoutes(r, uniIndex)

	// exam_bundling
	exam_bundles.RegisterBundleRoutes(r, a.DBx)

	// user goal tracker
	custom.RegisterUserGoalTrackerRoutes(r, a.DBx)

	// snbt goal tracker
	custom.RegisterSnbtGoalTrackerRoutes(r, a.DBx)

	a.Router = r
}

func (a *App) Run() {
	port := getPort()

	// Create a custom HTTP server
	server := &http.Server{
		Addr:           ":" + port,
		Handler:        a.Router,
		ReadTimeout:    120 * time.Second, // Adjust as necessary
		WriteTimeout:   120 * time.Second, // Adjust as necessary
		IdleTimeout:    120 * time.Second,
		MaxHeaderBytes: 1 << 20, // 1 MB
	}

	go func() {
		quit := make(chan os.Signal, 1) // Buffered channel with capacity 1
		signal.Notify(quit, os.Interrupt, syscall.SIGINT, syscall.SIGTERM, syscall.SIGHUP)

		sig := <-quit
		log.Printf("Received signal %v, starting graceful shutdown...\n", sig)

		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		if err := server.Shutdown(ctx); err != nil {
			log.Fatalf("Server forced to shutdown: %v", err)
		}

		log.Println("Server shutdown complete.")
	}()

	// Start the server
	log.Printf("Server starting on port %s\n", port)
	log.Fatal(server.ListenAndServe())
}

func readConfig(filepath string) Config {
	data, err := os.ReadFile(filepath)
	if err != nil {
		log.Fatalf("Error reading config file: %v", err)
	}

	var config Config
	err = yaml.Unmarshal(data, &config)
	if err != nil {
		log.Fatalf("Error parsing config file: %v", err)
	}

	return config
}

func getPort() string {
	// Check if the PORT environment variable exists (takes precedence)
	port := os.Getenv("PORT")
	if port != "" {
		return port
	}

	// Check if the APP_PORT environment variable exists
	port = os.Getenv("APP_PORT")
	if port != "" {
		return port
	}

	// Fall back to the YAML configuration
	config := readConfig("configs/config.yaml")
	return config.Run.Port
}
