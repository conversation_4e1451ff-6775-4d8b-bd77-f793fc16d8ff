{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GAWEAN/TERANG/terang-web-ui/components/snbt-goal-tracker/form.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport {\n  Input,\n  Button,\n  RadioGroup,\n  Radio,\n  Textarea,\n  Checkbox,\n  Card,\n  CardBody,\n  Divider\n} from \"@heroui/react\";\n\nimport { SnbtGoalTrackerFormData, FormErrors } from \"./actions\";\n\ninterface SnbtGoalTrackerFormProps {\n  initialData?: Partial<SnbtGoalTrackerFormData>;\n  onFormDataChange: (data: Partial<SnbtGoalTrackerFormData>) => void;\n  onComplete: () => void;\n  onDecline: () => void;\n  clearError: (field: string) => void;\n  formErrors: FormErrors;\n  isSubmitting: boolean;\n  userName?: string;\n  userEmail?: string;\n}\n\nexport function SnbtGoalTrackerForm({\n  initialData = {},\n  onFormDataChange,\n  onComplete,\n  onDecline,\n  clearError,\n  formErrors,\n  isSubmitting,\n  userName = \"\",\n  userEmail = \"\"\n}: SnbtGoalTrackerFormProps) {\n  const [formData, setFormData] = useState<Partial<SnbtGoalTrackerFormData>>({\n    name: userName || \"\",\n    email: userEmail || \"\",\n    penalaranUmum: 0,\n    pengetahuanKuantitatif: 0,\n    pengetahuanPemahamanUmum: 0,\n    pemahamanBacaanMenulis: 0,\n    literasiBahasaIndonesia: 0,\n    literasiBahasaInggris: 0,\n    penalaranMatematika: 0,\n    passedSnbt: false,\n    feltHelped: false,\n    helpfulnessRating: 5,\n    mostHelpfulAspect: \"\",\n    improvementSuggestions: \"\",\n    contactConsent: false,\n    phoneNumber: \"\",\n    ...initialData,\n  });\n\n  // Update form data when initialData changes\n  useEffect(() => {\n    if (userName && !formData.name) {\n      setFormData(prev => ({ ...prev, name: userName }));\n    }\n    if (userEmail && !formData.email) {\n      setFormData(prev => ({ ...prev, email: userEmail }));\n    }\n  }, [userName, userEmail, formData.name, formData.email]);\n\n  // Notify parent component of form data changes\n  useEffect(() => {\n    onFormDataChange(formData);\n  }, [formData, onFormDataChange]);\n\n  const handleInputChange = (field: keyof SnbtGoalTrackerFormData, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    clearError(field);\n  };\n\n  const handleDeclineClick = () => {\n    // Set minimal required data for decline\n    const declineData = {\n      ...formData,\n      name: formData.name || userName || \"User\",\n      email: formData.email || userEmail || \"\",\n      penalaranUmum: 0,\n      pengetahuanKuantitatif: 0,\n      pengetahuanPemahamanUmum: 0,\n      pemahamanBacaanMenulis: 0,\n      literasiBahasaIndonesia: 0,\n      literasiBahasaInggris: 0,\n      penalaranMatematika: 0,\n      passedSnbt: false,\n      feltHelped: false,\n      helpfulnessRating: 1,\n      mostHelpfulAspect: \"Tidak mengikuti SNBT atau latihan yang berkaitan\",\n      improvementSuggestions: \"-\",\n      contactConsent: false,\n      phoneNumber: \"-\",\n    };\n    setFormData(declineData);\n    onFormDataChange(declineData);\n    onDecline();\n  };\n\n\n\n  return (\n    <div className=\"w-full max-w-4xl mx-auto p-4 sm:p-6 space-y-6\">\n      {/* Decline Button */}\n      <div className=\"flex justify-center mb-6\">\n        <Button\n          color=\"danger\"\n          variant=\"solid\"\n          onPress={handleDeclineClick}\n          isDisabled={isSubmitting}\n          className=\"w-full sm:w-auto px-6 py-2 text-sm sm:text-base\"\n        >\n          Saya Tidak Mengikuti SNBT / Latihan yang Berkaitan\n        </Button>\n      </div>\n\n      <Divider />\n\n      {/* Personal Information */}\n      <Card className=\"bg-white shadow-sm\">\n        <CardBody className=\"p-4 sm:p-6\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"w-1 h-6 bg-primary rounded-full mr-3\"></div>\n            <h3 className=\"text-lg font-semibold\">Informasi Pribadi</h3>\n          </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <Input\n              label=\"Nama Lengkap\"\n              placeholder=\"Masukkan nama lengkap Anda\"\n              value={formData.name || \"\"}\n              onChange={(e) => handleInputChange('name', e.target.value)}\n              isInvalid={!!formErrors.name}\n              errorMessage={formErrors.name}\n              isRequired\n              className=\"w-full\"\n            />\n            <Input\n              label=\"Email\"\n              placeholder=\"Masukkan email Anda\"\n              type=\"email\"\n              value={formData.email || \"\"}\n              onChange={(e) => handleInputChange('email', e.target.value)}\n              isInvalid={!!formErrors.email}\n              errorMessage={formErrors.email}\n              isRequired\n              className=\"w-full\"\n            />\n          </div>\n        </CardBody>\n      </Card>\n\n      {/* SNBT Scores */}\n      <Card className=\"bg-white shadow-sm\">\n        <CardBody className=\"p-4 sm:p-6\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"w-1 h-6 bg-primary rounded-full mr-3\"></div>\n            <h3 className=\"text-lg font-semibold\">Skor SNBT 2025 Anda</h3>\n          </div>\n          <p className=\"text-sm text-gray-600 mb-6\">\n            Masukkan skor yang Anda peroleh untuk setiap komponen SNBT 2025\n          </p>\n\n          {/* Tes Skolastik Section */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center mb-4\">\n              <div className=\"w-1 h-5 bg-blue-500 rounded-full mr-2\"></div>\n              <h4 className=\"text-md font-semibold text-blue-700\">Tes Skolastik</h4>\n            </div>\n            <div className=\"bg-blue-50 p-4 rounded-lg border border-blue-200\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <Input\n                  label=\"Penalaran Umum\"\n                  placeholder=\"0\"\n                  type=\"number\"\n                  min=\"0\"\n                  value={formData.penalaranUmum?.toString() || \"0\"}\n                  onChange={(e) => handleInputChange('penalaranUmum', parseInt(e.target.value) || 0)}\n                  isInvalid={!!formErrors.penalaranUmum}\n                  errorMessage={formErrors.penalaranUmum}\n                  isRequired\n                  className=\"w-full\"\n                />\n                <Input\n                  label=\"Pengetahuan Kuantitatif\"\n                  placeholder=\"0\"\n                  type=\"number\"\n                  min=\"0\"\n                  value={formData.pengetahuanKuantitatif?.toString() || \"0\"}\n                  onChange={(e) => handleInputChange('pengetahuanKuantitatif', parseInt(e.target.value) || 0)}\n                  isInvalid={!!formErrors.pengetahuanKuantitatif}\n                  errorMessage={formErrors.pengetahuanKuantitatif}\n                  isRequired\n                  className=\"w-full\"\n                />\n                <Input\n                  label=\"Pengetahuan dan Pemahaman Umum\"\n                  placeholder=\"0\"\n                  type=\"number\"\n                  min=\"0\"\n                  value={formData.pengetahuanPemahamanUmum?.toString() || \"0\"}\n                  onChange={(e) => handleInputChange('pengetahuanPemahamanUmum', parseInt(e.target.value) || 0)}\n                  isInvalid={!!formErrors.pengetahuanPemahamanUmum}\n                  errorMessage={formErrors.pengetahuanPemahamanUmum}\n                  isRequired\n                  className=\"w-full\"\n                />\n                <Input\n                  label=\"Pemahaman Bacaan dan Menulis\"\n                  placeholder=\"0\"\n                  type=\"number\"\n                  min=\"0\"\n                  value={formData.pemahamanBacaanMenulis?.toString() || \"0\"}\n                  onChange={(e) => handleInputChange('pemahamanBacaanMenulis', parseInt(e.target.value) || 0)}\n                  isInvalid={!!formErrors.pemahamanBacaanMenulis}\n                  errorMessage={formErrors.pemahamanBacaanMenulis}\n                  isRequired\n                  className=\"w-full\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Penalaran Matematika Section */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center mb-4\">\n              <div className=\"w-1 h-5 bg-green-500 rounded-full mr-2\"></div>\n              <h4 className=\"text-md font-semibold text-green-700\">Penalaran Matematika</h4>\n            </div>\n            <div className=\"bg-green-50 p-4 rounded-lg border border-green-200\">\n              <div className=\"grid grid-cols-1 gap-4\">\n                <Input\n                  label=\"Penalaran Matematika\"\n                  placeholder=\"0\"\n                  type=\"number\"\n                  min=\"0\"\n                  value={formData.penalaranMatematika?.toString() || \"0\"}\n                  onChange={(e) => handleInputChange('penalaranMatematika', parseInt(e.target.value) || 0)}\n                  isInvalid={!!formErrors.penalaranMatematika}\n                  errorMessage={formErrors.penalaranMatematika}\n                  isRequired\n                  className=\"w-full\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Literasi Section */}\n          <div className=\"mb-6\">\n            <div className=\"flex items-center mb-4\">\n              <div className=\"w-1 h-5 bg-purple-500 rounded-full mr-2\"></div>\n              <h4 className=\"text-md font-semibold text-purple-700\">Literasi</h4>\n            </div>\n            <div className=\"bg-purple-50 p-4 rounded-lg border border-purple-200\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <Input\n                  label=\"Literasi Bahasa Indonesia\"\n                  placeholder=\"0\"\n                  type=\"number\"\n                  min=\"0\"\n                  value={formData.literasiBahasaIndonesia?.toString() || \"0\"}\n                  onChange={(e) => handleInputChange('literasiBahasaIndonesia', parseInt(e.target.value) || 0)}\n                  isInvalid={!!formErrors.literasiBahasaIndonesia}\n                  errorMessage={formErrors.literasiBahasaIndonesia}\n                  isRequired\n                  className=\"w-full\"\n                />\n                <Input\n                  label=\"Literasi Bahasa Inggris\"\n                  placeholder=\"0\"\n                  type=\"number\"\n                  min=\"0\"\n                  value={formData.literasiBahasaInggris?.toString() || \"0\"}\n                  onChange={(e) => handleInputChange('literasiBahasaInggris', parseInt(e.target.value) || 0)}\n                  isInvalid={!!formErrors.literasiBahasaInggris}\n                  errorMessage={formErrors.literasiBahasaInggris}\n                  isRequired\n                  className=\"w-full\"\n                />\n              </div>\n            </div>\n          </div>\n        </CardBody>\n      </Card>\n\n      {/* SNBT Result */}\n      <Card className=\"bg-white shadow-sm\">\n        <CardBody className=\"p-4 sm:p-6\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"w-1 h-6 bg-primary rounded-full mr-3\"></div>\n            <h3 className=\"text-lg font-semibold\">Hasil SNBT</h3>\n          </div>\n          <RadioGroup\n            label=\"Apakah Anda lulus SNBT 2025?\"\n            value={formData.passedSnbt ? \"true\" : \"false\"}\n            onValueChange={(value) => handleInputChange('passedSnbt', value === \"true\")}\n            className=\"mb-4\"\n          >\n            <Radio value=\"true\">Ya, saya lulus SNBT 2025</Radio>\n            <Radio value=\"false\">Tidak, saya tidak lulus SNBT 2025</Radio>\n          </RadioGroup>\n        </CardBody>\n      </Card>\n\n      {/* Terang AI Feedback */}\n      <Card className=\"bg-white shadow-sm\">\n        <CardBody className=\"p-4 sm:p-6\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"w-1 h-6 bg-primary rounded-full mr-3\"></div>\n            <h3 className=\"text-lg font-semibold\">Feedback tentang Terang AI</h3>\n          </div>\n          \n          <RadioGroup\n            label=\"Apakah Terang AI membantu Anda dalam persiapan SNBT?\"\n            value={formData.feltHelped ? \"true\" : \"false\"}\n            onValueChange={(value) => handleInputChange('feltHelped', value === \"true\")}\n            className=\"mb-6\"\n          >\n            <Radio value=\"true\">Ya, sangat membantu</Radio>\n            <Radio value=\"false\">Tidak terlalu membantu</Radio>\n          </RadioGroup>\n\n          <div className=\"mb-6\">\n            <label htmlFor=\"helpfulnessRating\" className=\"block text-sm font-medium mb-2\">\n              Seberapa Terbantu Terang AI untuk membantu persiapan proses ujian? (1-10)\n            </label>\n            <div className=\"flex flex-col sm:flex-row items-center gap-2\">\n              <div className=\"flex items-center gap-2 w-full\">\n                <span className=\"text-sm font-medium\">1</span>\n                <Input\n                  id=\"helpfulnessRating\"\n                  type=\"range\"\n                  min=\"1\"\n                  max=\"10\"\n                  value={formData.helpfulnessRating !== undefined ? formData.helpfulnessRating.toString() : '5'}\n                  onChange={(e) => {\n                    const value = e.target.value === '' ? 5 : parseInt(e.target.value);\n                    handleInputChange(\"helpfulnessRating\", value);\n                  }}\n                  className=\"flex-1\"\n                />\n                <span className=\"text-sm font-medium\">10</span>\n              </div>\n              <div className=\"flex items-center justify-center w-10 h-10 rounded-full bg-primary text-white font-bold\">\n                {formData.helpfulnessRating || 5}\n              </div>\n            </div>\n            {formErrors.helpfulnessRating && (\n              <p className=\"text-danger text-sm mt-1\">{formErrors.helpfulnessRating}</p>\n            )}\n          </div>\n\n          <Textarea\n            label=\"Aspek mana yang paling membantu?\"\n            placeholder=\"Ceritakan aspek Terang AI yang paling membantu dalam persiapan SNBT Anda...\"\n            value={formData.mostHelpfulAspect || \"\"}\n            onChange={(e) => handleInputChange('mostHelpfulAspect', e.target.value)}\n            className=\"mb-4\"\n            minRows={3}\n          />\n\n          <Textarea\n            label=\"Saran untuk perbaikan\"\n            placeholder=\"Apa yang bisa kami tingkatkan untuk membantu persiapan SNBT yang lebih baik?\"\n            value={formData.improvementSuggestions || \"\"}\n            onChange={(e) => handleInputChange('improvementSuggestions', e.target.value)}\n            className=\"mb-4\"\n            minRows={3}\n          />\n        </CardBody>\n      </Card>\n\n      {/* Contact Consent */}\n      <Card className=\"bg-white shadow-sm\">\n        <CardBody className=\"p-4 sm:p-6\">\n          <div className=\"flex items-center mb-4\">\n            <div className=\"w-1 h-6 bg-primary rounded-full mr-3\"></div>\n            <h3 className=\"text-lg font-semibold\">Izin Kontak</h3>\n          </div>\n          \n          <Checkbox\n            isSelected={formData.contactConsent || false}\n            onValueChange={(checked) => handleInputChange('contactConsent', checked)}\n            className=\"mb-4\"\n          >\n            Saya setuju untuk dihubungi oleh tim Terang AI untuk follow-up atau penelitian lebih lanjut\n          </Checkbox>\n\n          {formData.contactConsent && (\n            <Input\n              label=\"Nomor Telepon\"\n              placeholder=\"Masukkan nomor telepon Anda\"\n              value={formData.phoneNumber || \"\"}\n              onChange={(e) => handleInputChange('phoneNumber', e.target.value)}\n              isInvalid={!!formErrors.phoneNumber}\n              errorMessage={formErrors.phoneNumber}\n              className=\"w-full md:w-1/2\"\n            />\n          )}\n        </CardBody>\n      </Card>\n\n      {/* Submit Button */}\n      <div className=\"flex justify-center pt-6\">\n        <Button\n          color=\"primary\"\n          size=\"lg\"\n          onPress={onComplete}\n          isLoading={isSubmitting}\n          isDisabled={isSubmitting}\n          className=\"w-full sm:w-auto px-8 py-3 text-base font-medium\"\n        >\n          {isSubmitting ? \"Mengirim...\" : \"Kirim Survei\"}\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AA6BO,SAAS,oBAAoB,EAClC,cAAc,CAAC,CAAC,EAChB,gBAAgB,EAChB,UAAU,EACV,SAAS,EACT,UAAU,EACV,UAAU,EACV,YAAY,EACZ,WAAW,EAAE,EACb,YAAY,EAAE,EACW;;IACzB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC;QACzE,MAAM,YAAY;QAClB,OAAO,aAAa;QACpB,eAAe;QACf,wBAAwB;QACxB,0BAA0B;QAC1B,wBAAwB;QACxB,yBAAyB;QACzB,uBAAuB;QACvB,qBAAqB;QACrB,YAAY;QACZ,YAAY;QACZ,mBAAmB;QACnB,mBAAmB;QACnB,wBAAwB;QACxB,gBAAgB;QAChB,aAAa;QACb,GAAG,WAAW;IAChB;IAEA,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,YAAY,CAAC,SAAS,IAAI,EAAE;gBAC9B;qDAAY,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,MAAM;wBAAS,CAAC;;YAClD;YACA,IAAI,aAAa,CAAC,SAAS,KAAK,EAAE;gBAChC;qDAAY,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,OAAO;wBAAU,CAAC;;YACpD;QACF;wCAAG;QAAC;QAAU;QAAW,SAAS,IAAI;QAAE,SAAS,KAAK;KAAC;IAEvD,+CAA+C;IAC/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,iBAAiB;QACnB;wCAAG;QAAC;QAAU;KAAiB;IAE/B,MAAM,oBAAoB,CAAC,OAAsC;QAC/D,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,WAAW;IACb;IAEA,MAAM,qBAAqB;QACzB,wCAAwC;QACxC,MAAM,cAAc;YAClB,GAAG,QAAQ;YACX,MAAM,SAAS,IAAI,IAAI,YAAY;YACnC,OAAO,SAAS,KAAK,IAAI,aAAa;YACtC,eAAe;YACf,wBAAwB;YACxB,0BAA0B;YAC1B,wBAAwB;YACxB,yBAAyB;YACzB,uBAAuB;YACvB,qBAAqB;YACrB,YAAY;YACZ,YAAY;YACZ,mBAAmB;YACnB,mBAAmB;YACnB,wBAAwB;YACxB,gBAAgB;YAChB,aAAa;QACf;QACA,YAAY;QACZ,iBAAiB;QACjB;IACF;IAIA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+MAAA,CAAA,SAAM;oBACL,OAAM;oBACN,SAAQ;oBACR,SAAS;oBACT,YAAY;oBACZ,WAAU;8BACX;;;;;;;;;;;0BAKH,6LAAC,kNAAA,CAAA,UAAO;;;;;0BAGR,6LAAC,yMAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,kNAAA,CAAA,WAAQ;oBAAC,WAAU;;sCAClB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAG,WAAU;8CAAwB;;;;;;;;;;;;sCAExC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,iPAAA,CAAA,QAAK;oCACJ,OAAM;oCACN,aAAY;oCACZ,OAAO,SAAS,IAAI,IAAI;oCACxB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;oCACzD,WAAW,CAAC,CAAC,WAAW,IAAI;oCAC5B,cAAc,WAAW,IAAI;oCAC7B,UAAU;oCACV,WAAU;;;;;;8CAEZ,6LAAC,iPAAA,CAAA,QAAK;oCACJ,OAAM;oCACN,aAAY;oCACZ,MAAK;oCACL,OAAO,SAAS,KAAK,IAAI;oCACzB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;oCAC1D,WAAW,CAAC,CAAC,WAAW,KAAK;oCAC7B,cAAc,WAAW,KAAK;oCAC9B,UAAU;oCACV,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAOlB,6LAAC,yMAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,kNAAA,CAAA,WAAQ;oBAAC,WAAU;;sCAClB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAG,WAAU;8CAAwB;;;;;;;;;;;;sCAExC,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAK1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAG,WAAU;sDAAsC;;;;;;;;;;;;8CAEtD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iPAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,aAAY;gDACZ,MAAK;gDACL,KAAI;gDACJ,OAAO,SAAS,aAAa,EAAE,cAAc;gDAC7C,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gDAChF,WAAW,CAAC,CAAC,WAAW,aAAa;gDACrC,cAAc,WAAW,aAAa;gDACtC,UAAU;gDACV,WAAU;;;;;;0DAEZ,6LAAC,iPAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,aAAY;gDACZ,MAAK;gDACL,KAAI;gDACJ,OAAO,SAAS,sBAAsB,EAAE,cAAc;gDACtD,UAAU,CAAC,IAAM,kBAAkB,0BAA0B,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gDACzF,WAAW,CAAC,CAAC,WAAW,sBAAsB;gDAC9C,cAAc,WAAW,sBAAsB;gDAC/C,UAAU;gDACV,WAAU;;;;;;0DAEZ,6LAAC,iPAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,aAAY;gDACZ,MAAK;gDACL,KAAI;gDACJ,OAAO,SAAS,wBAAwB,EAAE,cAAc;gDACxD,UAAU,CAAC,IAAM,kBAAkB,4BAA4B,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gDAC3F,WAAW,CAAC,CAAC,WAAW,wBAAwB;gDAChD,cAAc,WAAW,wBAAwB;gDACjD,UAAU;gDACV,WAAU;;;;;;0DAEZ,6LAAC,iPAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,aAAY;gDACZ,MAAK;gDACL,KAAI;gDACJ,OAAO,SAAS,sBAAsB,EAAE,cAAc;gDACtD,UAAU,CAAC,IAAM,kBAAkB,0BAA0B,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gDACzF,WAAW,CAAC,CAAC,WAAW,sBAAsB;gDAC9C,cAAc,WAAW,sBAAsB;gDAC/C,UAAU;gDACV,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAOlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAG,WAAU;sDAAuC;;;;;;;;;;;;8CAEvD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iPAAA,CAAA,QAAK;4CACJ,OAAM;4CACN,aAAY;4CACZ,MAAK;4CACL,KAAI;4CACJ,OAAO,SAAS,mBAAmB,EAAE,cAAc;4CACnD,UAAU,CAAC,IAAM,kBAAkB,uBAAuB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;4CACtF,WAAW,CAAC,CAAC,WAAW,mBAAmB;4CAC3C,cAAc,WAAW,mBAAmB;4CAC5C,UAAU;4CACV,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAOlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAG,WAAU;sDAAwC;;;;;;;;;;;;8CAExD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,iPAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,aAAY;gDACZ,MAAK;gDACL,KAAI;gDACJ,OAAO,SAAS,uBAAuB,EAAE,cAAc;gDACvD,UAAU,CAAC,IAAM,kBAAkB,2BAA2B,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gDAC1F,WAAW,CAAC,CAAC,WAAW,uBAAuB;gDAC/C,cAAc,WAAW,uBAAuB;gDAChD,UAAU;gDACV,WAAU;;;;;;0DAEZ,6LAAC,iPAAA,CAAA,QAAK;gDACJ,OAAM;gDACN,aAAY;gDACZ,MAAK;gDACL,KAAI;gDACJ,OAAO,SAAS,qBAAqB,EAAE,cAAc;gDACrD,UAAU,CAAC,IAAM,kBAAkB,yBAAyB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gDACxF,WAAW,CAAC,CAAC,WAAW,qBAAqB;gDAC7C,cAAc,WAAW,qBAAqB;gDAC9C,UAAU;gDACV,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStB,6LAAC,yMAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,kNAAA,CAAA,WAAQ;oBAAC,WAAU;;sCAClB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAG,WAAU;8CAAwB;;;;;;;;;;;;sCAExC,6LAAC,uNAAA,CAAA,aAAU;4BACT,OAAM;4BACN,OAAO,SAAS,UAAU,GAAG,SAAS;4BACtC,eAAe,CAAC,QAAU,kBAAkB,cAAc,UAAU;4BACpE,WAAU;;8CAEV,6LAAC,4MAAA,CAAA,QAAK;oCAAC,OAAM;8CAAO;;;;;;8CACpB,6LAAC,4MAAA,CAAA,QAAK;oCAAC,OAAM;8CAAQ;;;;;;;;;;;;;;;;;;;;;;;0BAM3B,6LAAC,yMAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,kNAAA,CAAA,WAAQ;oBAAC,WAAU;;sCAClB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAG,WAAU;8CAAwB;;;;;;;;;;;;sCAGxC,6LAAC,uNAAA,CAAA,aAAU;4BACT,OAAM;4BACN,OAAO,SAAS,UAAU,GAAG,SAAS;4BACtC,eAAe,CAAC,QAAU,kBAAkB,cAAc,UAAU;4BACpE,WAAU;;8CAEV,6LAAC,4MAAA,CAAA,QAAK;oCAAC,OAAM;8CAAO;;;;;;8CACpB,6LAAC,4MAAA,CAAA,QAAK;oCAAC,OAAM;8CAAQ;;;;;;;;;;;;sCAGvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,SAAQ;oCAAoB,WAAU;8CAAiC;;;;;;8CAG9E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,6LAAC,iPAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,KAAI;oDACJ,KAAI;oDACJ,OAAO,SAAS,iBAAiB,KAAK,YAAY,SAAS,iBAAiB,CAAC,QAAQ,KAAK;oDAC1F,UAAU,CAAC;wDACT,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK,KAAK,KAAK,IAAI,SAAS,EAAE,MAAM,CAAC,KAAK;wDACjE,kBAAkB,qBAAqB;oDACzC;oDACA,WAAU;;;;;;8DAEZ,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;;;;;;;sDAExC,6LAAC;4CAAI,WAAU;sDACZ,SAAS,iBAAiB,IAAI;;;;;;;;;;;;gCAGlC,WAAW,iBAAiB,kBAC3B,6LAAC;oCAAE,WAAU;8CAA4B,WAAW,iBAAiB;;;;;;;;;;;;sCAIzE,6LAAC,uPAAA,CAAA,WAAQ;4BACP,OAAM;4BACN,aAAY;4BACZ,OAAO,SAAS,iBAAiB,IAAI;4BACrC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;4BACtE,WAAU;4BACV,SAAS;;;;;;sCAGX,6LAAC,uPAAA,CAAA,WAAQ;4BACP,OAAM;4BACN,aAAY;4BACZ,OAAO,SAAS,sBAAsB,IAAI;4BAC1C,UAAU,CAAC,IAAM,kBAAkB,0BAA0B,EAAE,MAAM,CAAC,KAAK;4BAC3E,WAAU;4BACV,SAAS;;;;;;;;;;;;;;;;;0BAMf,6LAAC,yMAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6LAAC,kNAAA,CAAA,WAAQ;oBAAC,WAAU;;sCAClB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAG,WAAU;8CAAwB;;;;;;;;;;;;sCAGxC,6LAAC,qNAAA,CAAA,WAAQ;4BACP,YAAY,SAAS,cAAc,IAAI;4BACvC,eAAe,CAAC,UAAY,kBAAkB,kBAAkB;4BAChE,WAAU;sCACX;;;;;;wBAIA,SAAS,cAAc,kBACtB,6LAAC,iPAAA,CAAA,QAAK;4BACJ,OAAM;4BACN,aAAY;4BACZ,OAAO,SAAS,WAAW,IAAI;4BAC/B,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;4BAChE,WAAW,CAAC,CAAC,WAAW,WAAW;4BACnC,cAAc,WAAW,WAAW;4BACpC,WAAU;;;;;;;;;;;;;;;;;0BAOlB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+MAAA,CAAA,SAAM;oBACL,OAAM;oBACN,MAAK;oBACL,SAAS;oBACT,WAAW;oBACX,YAAY;oBACZ,WAAU;8BAET,eAAe,gBAAgB;;;;;;;;;;;;;;;;;AAK1C;GA1YgB;KAAA"}}, {"offset": {"line": 865, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 871, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GAWEAN/TERANG/terang-web-ui/components/snbt-goal-tracker/actions.ts"], "sourcesContent": ["\"use server\";\n\nimport { auth } from \"@/auth\";\n\nexport interface SnbtGoalTrackerFormData {\n  name: string;\n  email: string;\n  penalaranUmum: number;\n  pengetahuanKuantitatif: number;\n  pengetahuanPemahamanUmum: number;\n  pemahamanBacaanMenulis: number;\n  literasiBahasaIndonesia: number;\n  literasiBahasaInggris: number;\n  penalaranMatematika: number;\n  passedSnbt: boolean;\n  feltHelped: boolean;\n  helpfulnessRating: number;\n  mostHelpfulAspect: string;\n  improvementSuggestions: string;\n  contactConsent: boolean;\n  phoneNumber: string;\n}\n\nexport interface FormErrors {\n  [key: string]: string;\n}\n\nexport interface SubmissionResult {\n  success: boolean;\n  message?: string;\n  data?: any;\n}\n\n// Validate SNBT goal tracker form data\nexport async function validateSnbtGoalTrackerForm(data: SnbtGoalTrackerFormData): Promise<FormErrors> {\n  const errors: FormErrors = {};\n\n  // Validate required fields\n  if (!data.name || data.name.trim().length === 0) {\n    errors.name = \"Nama harus diisi\";\n  }\n\n  if (!data.email || data.email.trim().length === 0) {\n    errors.email = \"Email harus diisi\";\n  } else if (!/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.email)) {\n    errors.email = \"Format email tidak valid\";\n  }\n\n  // Validate score fields (should be non-negative numbers)\n  const scoreFields = [\n    { field: 'penalaranUmum', label: 'Penalaran Umum' },\n    { field: 'pengetahuanKuantitatif', label: 'Pengetahuan Kuantitatif' },\n    { field: 'pengetahuanPemahamanUmum', label: 'Pengetahuan dan Pemahaman Umum' },\n    { field: 'pemahamanBacaanMenulis', label: 'Pemahaman Bacaan dan Menulis' },\n    { field: 'literasiBahasaIndonesia', label: 'Literasi Bahasa Indonesia' },\n    { field: 'literasiBahasaInggris', label: 'Literasi Bahasa Inggris' },\n    { field: 'penalaranMatematika', label: 'Penalaran Matematika' }\n  ];\n\n  scoreFields.forEach(({ field, label }) => {\n    const value = data[field as keyof SnbtGoalTrackerFormData] as number;\n    if (value === undefined || value === null || isNaN(value) || value < 0) {\n      errors[field] = `${label} harus berupa angka yang valid (minimal 0)`;\n    }\n  });\n\n  // Validate helpfulness rating\n  if (data.helpfulnessRating === undefined || data.helpfulnessRating === null || \n      isNaN(data.helpfulnessRating) || data.helpfulnessRating < 1 || data.helpfulnessRating > 10) {\n    errors.helpfulnessRating = \"Rating kepuasan harus antara 1-10\";\n  }\n\n  // Validate phone number if contact consent is given\n  if (data.contactConsent && (!data.phoneNumber || data.phoneNumber.trim().length === 0)) {\n    errors.phoneNumber = \"Nomor telepon harus diisi jika Anda setuju untuk dihubungi\";\n  }\n\n  return errors;\n}\n\n// Submit SNBT goal tracker form\nexport async function submitSnbtGoalTrackerForm(data: SnbtGoalTrackerFormData): Promise<SubmissionResult> {\n  try {\n    console.log(\"[SNBT Goal Tracker Actions] Submitting form data:\", data);\n\n    const response = await fetch(`${process.env.BACKEND_BASE_URL}/v0/snbt-goal-tracker`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-API-KEY': process.env.BACKEND_API_KEY as string,\n      },\n      body: JSON.stringify({\n        name: data.name,\n        email: data.email,\n        penalaran_umum: data.penalaranUmum,\n        pengetahuan_kuantitatif: data.pengetahuanKuantitatif,\n        pengetahuan_pemahaman_umum: data.pengetahuanPemahamanUmum,\n        pemahaman_bacaan_menulis: data.pemahamanBacaanMenulis,\n        literasi_bahasa_indonesia: data.literasiBahasaIndonesia,\n        literasi_bahasa_inggris: data.literasiBahasaInggris,\n        penalaran_matematika: data.penalaranMatematika,\n        passed_snbt: data.passedSnbt,\n        felt_helped: data.feltHelped,\n        helpfulness_rating: data.helpfulnessRating,\n        most_helpful_aspect: data.mostHelpfulAspect,\n        improvement_suggestions: data.improvementSuggestions,\n        contact_consent: data.contactConsent,\n        phone_number: data.phoneNumber,\n      }),\n    });\n\n    const result = await response.json();\n\n    if (!response.ok) {\n      console.error(\"[SNBT Goal Tracker Actions] Submission failed:\", result);\n      return {\n        success: false,\n        message: result.message || `HTTP error! status: ${response.status}`,\n      };\n    }\n\n    console.log(\"[SNBT Goal Tracker Actions] Submission successful:\", result);\n    return {\n      success: true,\n      message: \"Form berhasil dikirim\",\n      data: result.data,\n    };\n  } catch (error) {\n    console.error(\"[SNBT Goal Tracker Actions] Error submitting form:\", error);\n    return {\n      success: false,\n      message: error instanceof Error ? error.message : \"Terjadi kesalahan tidak diketahui\",\n    };\n  }\n}\n\n// Check if user has already submitted SNBT goal tracker form\nexport async function checkSnbtGoalTrackerSubmission(email?: string): Promise<boolean> {\n  try {\n    // If no email provided, try to get from session\n    let userEmail = email;\n    if (!userEmail) {\n      const session = await auth();\n      userEmail = session?.user?.email || \"\";\n    }\n\n    if (!userEmail) {\n      console.log(\"[SNBT Goal Tracker Actions] No email available for submission check\");\n      return false;\n    }\n\n    console.log(\"[SNBT Goal Tracker Actions] Checking submission status for:\", userEmail);\n\n    const response = await fetch(\n      `${process.env.BACKEND_BASE_URL}/v0/snbt-goal-tracker/check-submission?email=${encodeURIComponent(userEmail)}`,\n      {\n        method: 'GET',\n        headers: {\n          'X-API-KEY': process.env.BACKEND_API_KEY as string,\n        },\n      }\n    );\n\n    if (!response.ok) {\n      console.error(\"[SNBT Goal Tracker Actions] Failed to check submission status:\", response.status);\n      return false;\n    }\n\n    const result = await response.json();\n    console.log(\"[SNBT Goal Tracker Actions] Submission check result:\", result);\n\n    return result.submitted || false;\n  } catch (error) {\n    console.error(\"[SNBT Goal Tracker Actions] Error checking submission:\", error);\n    return false;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;IAkCsB;IA+CA;IAwDA"}}, {"offset": {"line": 881, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 887, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GAWEAN/TERANG/terang-web-ui/app/snbt-survey/actions.ts"], "sourcesContent": ["\"use server\";\n\nimport { auth } from \"@/auth\";\n\nexport interface UserData {\n  name: string;\n  email: string;\n}\n\nexport async function getUserData(email?: string): Promise<UserData | null> {\n  try {\n    // If email is provided (from URL params), use it directly\n    if (email) {\n      console.log(\"[SNBT Survey] Using provided email:\", email);\n      \n      // Try to fetch user data from backend\n      const response = await fetch(\n        `${process.env.BACKEND_BASE_URL}/v0/users/profile?email=${encodeURIComponent(email)}`,\n        {\n          method: 'GET',\n          headers: {\n            'X-API-KEY': process.env.BACKEND_API_KEY as string,\n          },\n        }\n      );\n\n      if (response.ok) {\n        const userData = await response.json();\n        console.log(\"[SNBT Survey] User data fetched from backend:\", userData);\n        return {\n          name: userData.name || \"\",\n          email: userData.email || email,\n        };\n      } else {\n        console.log(\"[SNBT Survey] User not found in backend, using email only\");\n        return {\n          name: \"\",\n          email: email,\n        };\n      }\n    }\n\n    // If no email provided, try to get from session\n    const session = await auth();\n    if (session?.user) {\n      console.log(\"[SNBT Survey] Using session data:\", session.user);\n      return {\n        name: session.user.name || \"\",\n        email: session.user.email || \"\",\n      };\n    }\n\n    console.log(\"[SNBT Survey] No user data available\");\n    return null;\n  } catch (error) {\n    console.error(\"[SNBT Survey] Error getting user data:\", error);\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;;;IASsB"}}, {"offset": {"line": 893, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 899, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GAWEAN/TERANG/terang-web-ui/app/snbt-survey/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useEffect } from \"react\";\nimport {\n  <PERSON><PERSON>,\n  <PERSON>,\n  CardHeader,\n  CardBody\n} from \"@heroui/react\";\nimport { toast, ToastContainer } from \"react-toastify\";\nimport \"react-toastify/dist/ReactToastify.css\";\n\nimport { SnbtGoalTrackerForm } from \"@/components/snbt-goal-tracker/form\";\nimport {\n  SnbtGoalTrackerFormData,\n  FormErrors,\n  validateSnbtGoalTrackerForm,\n  submitSnbtGoalTrackerForm\n} from \"@/components/snbt-goal-tracker/actions\";\nimport { getUserData } from \"./actions\";\n\nexport default function SnbtSurveyPage() {\n  // State variables\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [showSuccessScreen, setShowSuccessScreen] = useState(false);\n  const [formData, setFormData] = useState<Partial<SnbtGoalTrackerFormData>>({});\n  const [formErrors, setFormErrors] = useState<FormErrors>({});\n  const [userData, setUserData] = useState<{ name: string; email: string } | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  // Notification helper\n  const showNotification = (message: string, type: \"success\" | \"error\" | \"info\") => {\n    toast[type](message, {\n      position: \"top-center\",\n      autoClose: type === \"success\" ? 3000 : 5000,\n      hideProgressBar: false,\n      closeOnClick: true,\n      pauseOnHover: true,\n      draggable: true,\n      className: \"text-center\",\n      style: {\n        minWidth: '300px',\n        borderRadius: '10px',\n        fontWeight: type === \"success\" ? 'bold' : 'normal'\n      }\n    });\n  };\n\n  // Load user data on component mount\n  useEffect(() => {\n    const loadUserData = async () => {\n      setIsLoading(true);\n      try {\n        // Check if email is provided in URL params\n        const urlParams = new URLSearchParams(window.location.search);\n        const emailParam = urlParams.get('email');\n        \n        const data = await getUserData(emailParam || undefined);\n        setUserData(data);\n        \n        if (data) {\n          console.log(\"[SNBT Survey] User data loaded:\", data);\n        } else {\n          console.log(\"[SNBT Survey] No user data available\");\n        }\n      } catch (error) {\n        console.error(\"[SNBT Survey] Error loading user data:\", error);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    loadUserData();\n  }, []);\n\n  // Clear error for a specific field\n  const handleClearError = (field: keyof SnbtGoalTrackerFormData) => {\n    setFormErrors((prev) => {\n      const newErrors = { ...prev };\n      delete newErrors[field];\n      return newErrors;\n    });\n  };\n\n  // Validate the form\n  const validateForm = async (data: SnbtGoalTrackerFormData): Promise<FormErrors> => {\n    return await validateSnbtGoalTrackerForm(data);\n  };\n\n  // Function handlers\n  const handleFormDataChange = (data: Partial<SnbtGoalTrackerFormData>) => {\n    setFormData(data);\n  };\n\n  // Handle form decline (user didn't participate in SNBT)\n  const handleFormDecline = async () => {\n    try {\n      setIsSubmitting(true);\n\n      // Create minimal form data with just name and email\n      const minimalFormData: SnbtGoalTrackerFormData = {\n        name: formData.name || userData?.name || \"User\",\n        email: formData.email || userData?.email || \"\",\n        penalaranUmum: 0,\n        pengetahuanKuantitatif: 0,\n        pengetahuanPemahamanUmum: 0,\n        pemahamanBacaanMenulis: 0,\n        literasiBahasaIndonesia: 0,\n        literasiBahasaInggris: 0,\n        penalaranMatematika: 0,\n        passedSnbt: false,\n        feltHelped: false,\n        helpfulnessRating: 1,\n        mostHelpfulAspect: \"Tidak mengikuti SNBT atau latihan yang berkaitan\",\n        improvementSuggestions: \"-\",\n        contactConsent: false,\n        phoneNumber: \"-\",\n      };\n\n      console.log(\"[SNBT Survey] Submitting minimal data (user declined):\", minimalFormData);\n      const result = await submitSnbtGoalTrackerForm(minimalFormData);\n\n      if (result.success) {\n        // Show success screen\n        setShowSuccessScreen(true);\n\n        // Log successful submission\n        console.log(\"[SNBT Survey] Form declined and submitted successfully\");\n\n        // Show success notification\n        showNotification(\"Terima kasih atas respon Anda\", \"success\");\n\n        // Redirect after a delay\n        setTimeout(() => {\n          window.location.href = '/';\n        }, 3000);\n      } else {\n        // Log submission failure\n        console.error(\"[SNBT Survey] Form decline submission failed:\", result.message);\n\n        // Show error notification\n        showNotification(result.message || \"Gagal menyimpan data\", \"error\");\n      }\n    } catch (error) {\n      // Log submission error\n      console.error(\"[SNBT Survey] Error submitting declined form:\", error);\n\n      // Show error notification\n      const errorMessage = error instanceof Error\n        ? error.message\n        : \"Terjadi kesalahan tidak diketahui\";\n      showNotification(errorMessage, \"error\");\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const handleFormSubmit = async () => {\n    if (!formData || Object.keys(formData).length === 0) {\n      showNotification(\"Silakan isi formulir terlebih dahulu\", \"error\");\n      setFormErrors({ form: \"Please fill out the form\" });\n      return;\n    }\n\n    // Ensure all numeric fields are actually numbers\n    const processedFormData = {\n      ...formData,\n      penalaranUmum: formData.penalaranUmum !== undefined ? Number(formData.penalaranUmum) : undefined,\n      pengetahuanKuantitatif: formData.pengetahuanKuantitatif !== undefined ? Number(formData.pengetahuanKuantitatif) : undefined,\n      pengetahuanPemahamanUmum: formData.pengetahuanPemahamanUmum !== undefined ? Number(formData.pengetahuanPemahamanUmum) : undefined,\n      pemahamanBacaanMenulis: formData.pemahamanBacaanMenulis !== undefined ? Number(formData.pemahamanBacaanMenulis) : undefined,\n      literasiBahasaIndonesia: formData.literasiBahasaIndonesia !== undefined ? Number(formData.literasiBahasaIndonesia) : undefined,\n      literasiBahasaInggris: formData.literasiBahasaInggris !== undefined ? Number(formData.literasiBahasaInggris) : undefined,\n      penalaranMatematika: formData.penalaranMatematika !== undefined ? Number(formData.penalaranMatematika) : undefined,\n      helpfulnessRating: formData.helpfulnessRating !== undefined ? Number(formData.helpfulnessRating) : undefined,\n    };\n\n    console.log(\"[SNBT Survey] Form data before validation:\", processedFormData);\n\n    // Check if all required fields are present\n    const requiredFields: (keyof SnbtGoalTrackerFormData)[] = [\n      'name', 'email', 'penalaranUmum', 'pengetahuanKuantitatif', 'pengetahuanPemahamanUmum',\n      'pemahamanBacaanMenulis', 'literasiBahasaIndonesia', 'literasiBahasaInggris',\n      'penalaranMatematika', 'helpfulnessRating'\n    ];\n\n    const missingFields = requiredFields.filter(field => {\n      const value = processedFormData[field];\n      return value === undefined || value === null || value === '';\n    });\n\n    if (missingFields.length > 0) {\n      console.log(\"[SNBT Survey] Missing fields:\", missingFields);\n      showNotification(`Silakan lengkapi semua field yang diperlukan`, \"error\");\n      const errors: FormErrors = {};\n      missingFields.forEach(field => {\n        errors[field] = `Field ${field} harus diisi`;\n      });\n      setFormErrors(errors);\n      return;\n    }\n\n    // Cast to complete form data since we've verified required fields\n    const completeFormData = processedFormData as SnbtGoalTrackerFormData;\n\n    // Validate the form\n    const errors = await validateForm(completeFormData);\n    if (Object.keys(errors).length > 0) {\n      console.log(\"[SNBT Survey] Validation errors:\", errors);\n      const errorMessages = Object.values(errors).join(\", \");\n      showNotification(`Kesalahan validasi: ${errorMessages}`, \"error\");\n      setFormErrors(errors);\n      return;\n    }\n\n    try {\n      setIsSubmitting(true);\n\n      console.log(\"[SNBT Survey] Submitting form data:\", completeFormData);\n      const result = await submitSnbtGoalTrackerForm(completeFormData);\n\n      if (result.success) {\n        // Show success screen\n        setShowSuccessScreen(true);\n\n        // Log successful submission\n        console.log(\"[SNBT Survey] Form submitted successfully\");\n\n        // Show success notification\n        showNotification(\"Data berhasil disimpan!\", \"success\");\n\n        // Redirect after a delay\n        setTimeout(() => {\n          window.location.href = '/';\n        }, 5000);\n      } else {\n        // Log submission failure\n        console.error(\"[SNBT Survey] Form submission failed:\", result.message);\n\n        // Show error notification\n        showNotification(result.message || \"Gagal menyimpan data\", \"error\");\n      }\n    } catch (error) {\n      // Log submission error\n      console.error(\"[SNBT Survey] Error submitting form:\", error);\n\n      // Show error notification\n      const errorMessage = error instanceof Error\n        ? error.message\n        : \"Terjadi kesalahan tidak diketahui\";\n      showNotification(errorMessage, \"error\");\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"flex flex-col items-center justify-center min-h-screen p-4 bg-gray-50\">\n        <Spinner color=\"primary\" size=\"lg\" />\n        <p className=\"mt-4 text-gray-600\">Memuat...</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex flex-col items-center justify-center min-h-screen p-4 bg-gray-50\">\n      <Card className=\"w-full max-w-3xl\">\n        <CardHeader className=\"flex items-center gap-2 sm:gap-3 bg-white p-3 sm:p-4\">\n          <img\n            src=\"https://cdn.terang.ai/images/exams/SNBT_2025.png\"\n            alt=\"SNBT 2025 Logo\"\n            className=\"h-8 sm:h-10 w-auto\"\n          />\n          <div>\n            <h2 className=\"text-lg sm:text-xl font-medium\">Survei Hasil SNBT 2025</h2>\n            <p className=\"text-xs sm:text-sm text-gray-500 mt-1\">Bantu kami meningkatkan layanan untuk persiapan SNBT</p>\n          </div>\n        </CardHeader>\n        <CardBody className=\"p-0\">\n          {showSuccessScreen ? (\n            <div className=\"flex flex-col items-center justify-center py-8 sm:py-10 px-3 sm:px-4 text-center\">\n              <div className=\"w-16 h-16 sm:w-20 sm:h-20 rounded-full bg-green-100 flex items-center justify-center mb-4 sm:mb-6\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 sm:h-10 sm:w-10 text-green-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                </svg>\n              </div>\n              <h3 className=\"text-xl sm:text-2xl font-bold mb-2\">Terima Kasih!</h3>\n              <p className=\"text-base sm:text-lg mb-4 sm:mb-6\">Jawaban Anda telah berhasil disimpan.</p>\n              <p className=\"text-sm sm:text-base text-gray-600 mb-6 sm:mb-8\">\n                Kami sangat menghargai waktu dan masukan Anda. Informasi ini akan membantu kami meningkatkan layanan untuk persiapan SNBT.\n              </p>\n              <div className=\"flex items-center justify-center gap-3 sm:gap-4\">\n                <img\n                  src=\"https://cdn.terang.ai/images/exams/SNBT_2025.png\"\n                  alt=\"SNBT 2025 Logo\"\n                  className=\"h-10 sm:h-12 w-auto\"\n                />\n                <span className=\"text-lg sm:text-xl font-medium\">×</span>\n                <img\n                  src=\"https://cdn.terang.ai/images/logo/logo-terang-ai.svg\"\n                  alt=\"Terang AI Logo\"\n                  className=\"h-7 sm:h-8 w-auto\"\n                />\n              </div>\n              <p className=\"text-xs sm:text-sm text-gray-500 mt-6 sm:mt-8\">\n                Halaman akan dialihkan dalam beberapa detik...\n              </p>\n            </div>\n          ) : (\n            <div className=\"flex flex-col bg-gray-50 rounded-lg\">\n              <SnbtGoalTrackerForm\n                clearError={(field: string) => handleClearError(field as keyof SnbtGoalTrackerFormData)}\n                formErrors={formErrors}\n                initialData={formData}\n                onFormDataChange={handleFormDataChange}\n                onComplete={handleFormSubmit}\n                onDecline={handleFormDecline}\n                isSubmitting={isSubmitting}\n                userName={userData?.name || \"\"}\n                userEmail={userData?.email || \"\"}\n              />\n            </div>\n          )}\n        </CardBody>\n      </Card>\n      <ToastContainer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAOA;AAGA;AACA;AAMA;AAhBA;AAAA;AAAA;AAAA;;;AAHA;;;;;;;;AAqBe,SAAS;;IACtB,kBAAkB;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC,CAAC;IAC5E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAC1D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0C;IACjF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,sBAAsB;IACtB,MAAM,mBAAmB,CAAC,SAAiB;QACzC,yKAAA,CAAA,QAAK,CAAC,KAAK,CAAC,SAAS;YACnB,UAAU;YACV,WAAW,SAAS,YAAY,OAAO;YACvC,iBAAiB;YACjB,cAAc;YACd,cAAc;YACd,WAAW;YACX,WAAW;YACX,OAAO;gBACL,UAAU;gBACV,cAAc;gBACd,YAAY,SAAS,YAAY,SAAS;YAC5C;QACF;IACF;IAEA,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;yDAAe;oBACnB,aAAa;oBACb,IAAI;wBACF,2CAA2C;wBAC3C,MAAM,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;wBAC5D,MAAM,aAAa,UAAU,GAAG,CAAC;wBAEjC,MAAM,OAAO,MAAM,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD,EAAE,cAAc;wBAC7C,YAAY;wBAEZ,IAAI,MAAM;4BACR,QAAQ,GAAG,CAAC,mCAAmC;wBACjD,OAAO;4BACL,QAAQ,GAAG,CAAC;wBACd;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,0CAA0C;oBAC1D,SAAU;wBACR,aAAa;oBACf;gBACF;;YAEA;QACF;mCAAG,EAAE;IAEL,mCAAmC;IACnC,MAAM,mBAAmB,CAAC;QACxB,cAAc,CAAC;YACb,MAAM,YAAY;gBAAE,GAAG,IAAI;YAAC;YAC5B,OAAO,SAAS,CAAC,MAAM;YACvB,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,MAAM,eAAe,OAAO;QAC1B,OAAO,MAAM,CAAA,GAAA,mJAAA,CAAA,8BAA2B,AAAD,EAAE;IAC3C;IAEA,oBAAoB;IACpB,MAAM,uBAAuB,CAAC;QAC5B,YAAY;IACd;IAEA,wDAAwD;IACxD,MAAM,oBAAoB;QACxB,IAAI;YACF,gBAAgB;YAEhB,oDAAoD;YACpD,MAAM,kBAA2C;gBAC/C,MAAM,SAAS,IAAI,IAAI,UAAU,QAAQ;gBACzC,OAAO,SAAS,KAAK,IAAI,UAAU,SAAS;gBAC5C,eAAe;gBACf,wBAAwB;gBACxB,0BAA0B;gBAC1B,wBAAwB;gBACxB,yBAAyB;gBACzB,uBAAuB;gBACvB,qBAAqB;gBACrB,YAAY;gBACZ,YAAY;gBACZ,mBAAmB;gBACnB,mBAAmB;gBACnB,wBAAwB;gBACxB,gBAAgB;gBAChB,aAAa;YACf;YAEA,QAAQ,GAAG,CAAC,0DAA0D;YACtE,MAAM,SAAS,MAAM,CAAA,GAAA,mJAAA,CAAA,4BAAyB,AAAD,EAAE;YAE/C,IAAI,OAAO,OAAO,EAAE;gBAClB,sBAAsB;gBACtB,qBAAqB;gBAErB,4BAA4B;gBAC5B,QAAQ,GAAG,CAAC;gBAEZ,4BAA4B;gBAC5B,iBAAiB,iCAAiC;gBAElD,yBAAyB;gBACzB,WAAW;oBACT,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACzB,GAAG;YACL,OAAO;gBACL,yBAAyB;gBACzB,QAAQ,KAAK,CAAC,iDAAiD,OAAO,OAAO;gBAE7E,0BAA0B;gBAC1B,iBAAiB,OAAO,OAAO,IAAI,wBAAwB;YAC7D;QACF,EAAE,OAAO,OAAO;YACd,uBAAuB;YACvB,QAAQ,KAAK,CAAC,iDAAiD;YAE/D,0BAA0B;YAC1B,MAAM,eAAe,iBAAiB,QAClC,MAAM,OAAO,GACb;YACJ,iBAAiB,cAAc;QACjC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,YAAY,OAAO,IAAI,CAAC,UAAU,MAAM,KAAK,GAAG;YACnD,iBAAiB,wCAAwC;YACzD,cAAc;gBAAE,MAAM;YAA2B;YACjD;QACF;QAEA,iDAAiD;QACjD,MAAM,oBAAoB;YACxB,GAAG,QAAQ;YACX,eAAe,SAAS,aAAa,KAAK,YAAY,OAAO,SAAS,aAAa,IAAI;YACvF,wBAAwB,SAAS,sBAAsB,KAAK,YAAY,OAAO,SAAS,sBAAsB,IAAI;YAClH,0BAA0B,SAAS,wBAAwB,KAAK,YAAY,OAAO,SAAS,wBAAwB,IAAI;YACxH,wBAAwB,SAAS,sBAAsB,KAAK,YAAY,OAAO,SAAS,sBAAsB,IAAI;YAClH,yBAAyB,SAAS,uBAAuB,KAAK,YAAY,OAAO,SAAS,uBAAuB,IAAI;YACrH,uBAAuB,SAAS,qBAAqB,KAAK,YAAY,OAAO,SAAS,qBAAqB,IAAI;YAC/G,qBAAqB,SAAS,mBAAmB,KAAK,YAAY,OAAO,SAAS,mBAAmB,IAAI;YACzG,mBAAmB,SAAS,iBAAiB,KAAK,YAAY,OAAO,SAAS,iBAAiB,IAAI;QACrG;QAEA,QAAQ,GAAG,CAAC,8CAA8C;QAE1D,2CAA2C;QAC3C,MAAM,iBAAoD;YACxD;YAAQ;YAAS;YAAiB;YAA0B;YAC5D;YAA0B;YAA2B;YACrD;YAAuB;SACxB;QAED,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAA;YAC1C,MAAM,QAAQ,iBAAiB,CAAC,MAAM;YACtC,OAAO,UAAU,aAAa,UAAU,QAAQ,UAAU;QAC5D;QAEA,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,QAAQ,GAAG,CAAC,iCAAiC;YAC7C,iBAAiB,CAAC,4CAA4C,CAAC,EAAE;YACjE,MAAM,SAAqB,CAAC;YAC5B,cAAc,OAAO,CAAC,CAAA;gBACpB,MAAM,CAAC,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM,YAAY,CAAC;YAC9C;YACA,cAAc;YACd;QACF;QAEA,kEAAkE;QAClE,MAAM,mBAAmB;QAEzB,oBAAoB;QACpB,MAAM,SAAS,MAAM,aAAa;QAClC,IAAI,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG,GAAG;YAClC,QAAQ,GAAG,CAAC,oCAAoC;YAChD,MAAM,gBAAgB,OAAO,MAAM,CAAC,QAAQ,IAAI,CAAC;YACjD,iBAAiB,CAAC,oBAAoB,EAAE,eAAe,EAAE;YACzD,cAAc;YACd;QACF;QAEA,IAAI;YACF,gBAAgB;YAEhB,QAAQ,GAAG,CAAC,uCAAuC;YACnD,MAAM,SAAS,MAAM,CAAA,GAAA,mJAAA,CAAA,4BAAyB,AAAD,EAAE;YAE/C,IAAI,OAAO,OAAO,EAAE;gBAClB,sBAAsB;gBACtB,qBAAqB;gBAErB,4BAA4B;gBAC5B,QAAQ,GAAG,CAAC;gBAEZ,4BAA4B;gBAC5B,iBAAiB,2BAA2B;gBAE5C,yBAAyB;gBACzB,WAAW;oBACT,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACzB,GAAG;YACL,OAAO;gBACL,yBAAyB;gBACzB,QAAQ,KAAK,CAAC,yCAAyC,OAAO,OAAO;gBAErE,0BAA0B;gBAC1B,iBAAiB,OAAO,OAAO,IAAI,wBAAwB;YAC7D;QACF,EAAE,OAAO,OAAO;YACd,uBAAuB;YACvB,QAAQ,KAAK,CAAC,wCAAwC;YAEtD,0BAA0B;YAC1B,MAAM,eAAe,iBAAiB,QAClC,MAAM,OAAO,GACb;YACJ,iBAAiB,cAAc;QACjC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,kNAAA,CAAA,UAAO;oBAAC,OAAM;oBAAU,MAAK;;;;;;8BAC9B,6LAAC;oBAAE,WAAU;8BAAqB;;;;;;;;;;;;IAGxC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yMAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,sNAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,6LAAC;gCACC,KAAI;gCACJ,KAAI;gCACJ,WAAU;;;;;;0CAEZ,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAC/C,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;kCAGzD,6LAAC,kNAAA,CAAA,WAAQ;wBAAC,WAAU;kCACjB,kCACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,OAAM;wCAA6B,WAAU;wCAAyC,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDAChI,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;8CACjD,6LAAC;oCAAE,WAAU;8CAAkD;;;;;;8CAG/D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,KAAI;4CACJ,KAAI;4CACJ,WAAU;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAAiC;;;;;;sDACjD,6LAAC;4CACC,KAAI;4CACJ,KAAI;4CACJ,WAAU;;;;;;;;;;;;8CAGd,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;iDAK/D,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,iJAAA,CAAA,sBAAmB;gCAClB,YAAY,CAAC,QAAkB,iBAAiB;gCAChD,YAAY;gCACZ,aAAa;gCACb,kBAAkB;gCAClB,YAAY;gCACZ,WAAW;gCACX,cAAc;gCACd,UAAU,UAAU,QAAQ;gCAC5B,WAAW,UAAU,SAAS;;;;;;;;;;;;;;;;;;;;;;0BAMxC,6LAAC,yKAAA,CAAA,iBAAc;;;;;;;;;;;AAGrB;GApTwB;KAAA"}}, {"offset": {"line": 1367, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}